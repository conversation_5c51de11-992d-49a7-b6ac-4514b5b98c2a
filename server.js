// 导入HTTP模块
const http = require('http'); // 导入HTTP模块
const fs = require('fs'); // 导入文件系统模块
const path = require('path'); // 导入路径模块
const url = require('url'); // 导入URL模块

// 定义端口
const PORT = process.env.PORT || 3000; // 定义服务器端口

// MIME类型映射
const MIME_TYPES = {
    '.html': 'text/html', // HTML文件
    '.css': 'text/css', // CSS文件
    '.js': 'text/javascript', // JavaScript文件
    '.json': 'application/json', // JSON文件
    '.png': 'image/png', // PNG图片
    '.jpg': 'image/jpeg', // JPG图片
    '.jpeg': 'image/jpeg', // JPEG图片
    '.gif': 'image/gif', // GIF图片
    '.svg': 'image/svg+xml', // SVG图片
    '.ico': 'image/x-icon' // ICO图标
};

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    console.log(`请求: ${req.method} ${req.url}`); // 记录请求信息
    
    // 解析URL
    const parsedUrl = url.parse(req.url); // 解析URL
    
    // 规范化路径并解析请求的文件路径
    let pathname = path.normalize(parsedUrl.pathname); // 规范化路径
    
    // 如果请求根路径，返回index.html
    if (pathname === '/') {
        pathname = '/index.html'; // 默认返回index.html
    }
    
    // 获取文件的完整路径
    const filePath = path.join(__dirname, pathname); // 构建文件路径
    
    // 获取文件扩展名
    const extname = path.extname(filePath); // 获取文件扩展名
    
    // 获取MIME类型
    const contentType = MIME_TYPES[extname] || 'application/octet-stream'; // 获取内容类型
    
    // 读取文件
    fs.readFile(filePath, (err, content) => {
        if (err) {
            if (err.code === 'ENOENT') {
                // 文件不存在
                console.error(`文件不存在: ${filePath}`); // 记录错误
                res.writeHead(404); // 设置状态码
                res.end('404 Not Found'); // 结束响应
            } else {
                // 服务器错误
                console.error(`服务器错误: ${err.code}`); // 记录错误
                res.writeHead(500); // 设置状态码
                res.end(`Server Error: ${err.code}`); // 结束响应
            }
        } else {
            // 返回文件内容
            res.writeHead(200, { 'Content-Type': contentType }); // 设置状态码和内容类型
            res.end(content, 'utf-8'); // 结束响应
        }
    });
});

// 启动服务器
server.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`); // 记录服务器启动信息
}); 