const express = require('express');
const path = require('path');
const fs = require('fs');
const cors = require('cors');

const app = express();
const PORT = 3000;

// 中间件配置
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
app.use('/public', express.static(path.join(__dirname, 'public')));
app.use('/scripts', express.static(path.join(__dirname, 'scripts')));
app.use('/css', express.static(path.join(__dirname, 'css')));

// 模拟数据存储（后续可替换为MySQL）
let users = [
    {
        id: 1,
        username: 'sysadmin',
        password: 'sys123',
        role: 'system_admin',
        real_name: '系统管理员',
        email: '<EMAIL>',
        created_at: new Date().toISOString()
    },
    {
        id: 2,
        username: 'bizadmin',
        password: 'biz123',
        role: 'business_admin',
        real_name: '业务管理员',
        email: '<EMAIL>',
        created_at: new Date().toISOString()
    },
    {
        id: 3,
        username: 'user',
        password: 'password',
        role: 'user',
        real_name: '普通用户',
        email: '<EMAIL>',
        created_at: new Date().toISOString()
    }
];

let inventory = [
    { id: 1, name: '笔记本电脑', category: '电子设备', quantity: 15, price: 5000, supplier: '联想科技', min_stock: 5, max_stock: 50 },
    { id: 2, name: '办公椅', category: '办公家具', quantity: 8, price: 800, supplier: '宜家家居', min_stock: 10, max_stock: 30 },
    { id: 3, name: '打印机', category: '办公设备', quantity: 3, price: 2000, supplier: '惠普公司', min_stock: 5, max_stock: 15 },
    { id: 4, name: '文件柜', category: '办公家具', quantity: 12, price: 1200, supplier: '钢制家具', min_stock: 8, max_stock: 25 },
    { id: 5, name: '投影仪', category: '电子设备', quantity: 2, price: 3500, supplier: '爱普生', min_stock: 3, max_stock: 10 }
];

let requests = [
    { id: 1, user: 'user', item: '笔记本电脑', quantity: 2, purpose: '新员工配置', status: '待审批', date: '2024-05-01', type: 'outbound' },
    { id: 2, user: 'user', item: '办公椅', quantity: 3, purpose: '会议室布置', status: '已批准', date: '2024-04-28', type: 'outbound' },
    { id: 3, user: 'user', item: '打印机', quantity: 1, purpose: '部门使用', status: '待审批', date: '2024-05-02', type: 'outbound' }
];

let systemLogs = [
    { id: 1, timestamp: new Date().toISOString(), user: 'bizadmin', action: '添加物品', details: '添加了笔记本电脑', ip: '127.0.0.1' },
    { id: 2, timestamp: new Date().toISOString(), user: 'user', action: '提交申请', details: '申请办公椅 x2', ip: '127.0.0.1' },
    { id: 3, timestamp: new Date().toISOString(), user: 'bizadmin', action: '审批申请', details: '批准了用户的申请', ip: '127.0.0.1' }
];

// API路由

// 用户认证
app.post('/api/login', (req, res) => {
    const { username, password, role } = req.body;
    
    const user = users.find(u => 
        u.username === username && 
        u.password === password && 
        u.role === role
    );
    
    if (user) {
        // 记录登录日志
        systemLogs.push({
            id: systemLogs.length + 1,
            timestamp: new Date().toISOString(),
            user: username,
            action: '用户登录',
            details: `角色: ${role}`,
            ip: req.ip || '127.0.0.1'
        });
        
        res.json({
            success: true,
            message: '登录成功',
            user: {
                id: user.id,
                username: user.username,
                role: user.role,
                real_name: user.real_name
            }
        });
    } else {
        res.json({
            success: false,
            message: '用户名、密码或角色不正确'
        });
    }
});

// 用户注册
app.post('/api/register', (req, res) => {
    const { username, password } = req.body;
    
    if (users.find(u => u.username === username)) {
        return res.json({
            success: false,
            message: '用户名已存在'
        });
    }
    
    const newUser = {
        id: users.length + 1,
        username,
        password,
        role: 'user',
        real_name: username,
        email: '',
        created_at: new Date().toISOString()
    };
    
    users.push(newUser);
    
    // 记录注册日志
    systemLogs.push({
        id: systemLogs.length + 1,
        timestamp: new Date().toISOString(),
        user: 'system',
        action: '用户注册',
        details: `新用户注册: ${username}`,
        ip: req.ip || '127.0.0.1'
    });
    
    res.json({
        success: true,
        message: '注册成功'
    });
});

// 获取所有用户（系统管理员）
app.get('/api/users', (req, res) => {
    res.json({
        success: true,
        users: users.map(user => ({
            id: user.id,
            username: user.username,
            role: user.role,
            real_name: user.real_name,
            email: user.email,
            created_at: user.created_at
        }))
    });
});

// 添加用户（系统管理员）
app.post('/api/users', (req, res) => {
    const { username, password, role, real_name, email } = req.body;
    
    if (users.find(u => u.username === username)) {
        return res.json({
            success: false,
            message: '用户名已存在'
        });
    }
    
    const newUser = {
        id: users.length + 1,
        username,
        password,
        role: role || 'user',
        real_name: real_name || username,
        email: email || '',
        created_at: new Date().toISOString()
    };
    
    users.push(newUser);
    
    // 记录操作日志
    systemLogs.push({
        id: systemLogs.length + 1,
        timestamp: new Date().toISOString(),
        user: req.headers['x-user'] || 'system',
        action: '添加用户',
        details: `添加用户: ${username}, 角色: ${role}`,
        ip: req.ip || '127.0.0.1'
    });
    
    res.json({
        success: true,
        message: '用户添加成功',
        user: {
            id: newUser.id,
            username: newUser.username,
            role: newUser.role,
            real_name: newUser.real_name,
            email: newUser.email,
            created_at: newUser.created_at
        }
    });
});

// 删除用户（系统管理员）
app.delete('/api/users/:id', (req, res) => {
    const userId = parseInt(req.params.id);
    const userIndex = users.findIndex(u => u.id === userId);
    
    if (userIndex === -1) {
        return res.json({
            success: false,
            message: '用户不存在'
        });
    }
    
    if (userId === 1) {
        return res.json({
            success: false,
            message: '不能删除系统管理员账号'
        });
    }
    
    const deletedUser = users[userIndex];
    users.splice(userIndex, 1);
    
    // 记录操作日志
    systemLogs.push({
        id: systemLogs.length + 1,
        timestamp: new Date().toISOString(),
        user: req.headers['x-user'] || 'system',
        action: '删除用户',
        details: `删除用户: ${deletedUser.username}`,
        ip: req.ip || '127.0.0.1'
    });
    
    res.json({
        success: true,
        message: '用户删除成功'
    });
});

// 更新用户角色（系统管理员）
app.put('/api/users/:id/role', (req, res) => {
    const userId = parseInt(req.params.id);
    const { role } = req.body;
    const user = users.find(u => u.id === userId);
    
    if (!user) {
        return res.json({
            success: false,
            message: '用户不存在'
        });
    }
    
    if (userId === 1) {
        return res.json({
            success: false,
            message: '不能修改系统管理员的角色'
        });
    }
    
    const oldRole = user.role;
    user.role = role;
    
    // 记录操作日志
    systemLogs.push({
        id: systemLogs.length + 1,
        timestamp: new Date().toISOString(),
        user: req.headers['x-user'] || 'system',
        action: '更新用户角色',
        details: `用户: ${user.username}, 角色: ${oldRole} -> ${role}`,
        ip: req.ip || '127.0.0.1'
    });
    
    res.json({
        success: true,
        message: '用户角色更新成功',
        user: {
            id: user.id,
            username: user.username,
            role: user.role,
            real_name: user.real_name,
            email: user.email,
            created_at: user.created_at
        }
    });
});

// 获取系统日志（系统管理员）
app.get('/api/logs', (req, res) => {
    res.json({
        success: true,
        logs: systemLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
    });
});

// 获取系统统计（系统管理员）
app.get('/api/stats', (req, res) => {
    res.json({
        success: true,
        stats: {
            totalUsers: users.length,
            totalItems: inventory.length,
            totalRequests: requests.length,
            pendingRequests: requests.filter(r => r.status === '待审批').length,
            lowStockItems: inventory.filter(item => item.quantity <= item.min_stock).length,
            systemUptime: '15天 8小时 32分钟'
        }
    });
});

// 获取库存列表
app.get('/api/inventory', (req, res) => {
    res.json({
        success: true,
        items: inventory
    });
});

// 添加物品
app.post('/api/inventory', (req, res) => {
    const { name, category, price, supplier, description, min_stock, max_stock } = req.body;

    const newItem = {
        id: inventory.length + 1,
        name,
        category,
        quantity: 0,
        price: parseFloat(price) || 0,
        supplier,
        description,
        min_stock: parseInt(min_stock) || 10,
        max_stock: parseInt(max_stock) || 100,
        created_at: new Date().toISOString()
    };

    inventory.push(newItem);

    // 记录操作日志
    systemLogs.push({
        id: systemLogs.length + 1,
        timestamp: new Date().toISOString(),
        user: req.headers['x-user'] || 'system',
        action: '添加物品',
        details: `添加物品: ${name}`,
        ip: req.ip || '127.0.0.1'
    });

    res.json({
        success: true,
        message: '物品添加成功',
        item: newItem
    });
});

// 更新库存数量
app.put('/api/inventory/:id/quantity', (req, res) => {
    const itemId = parseInt(req.params.id);
    const { quantity } = req.body;
    const item = inventory.find(i => i.id === itemId);

    if (!item) {
        return res.json({
            success: false,
            message: '物品不存在'
        });
    }

    const oldQuantity = item.quantity;
    item.quantity = parseInt(quantity) || 0;

    // 记录操作日志
    systemLogs.push({
        id: systemLogs.length + 1,
        timestamp: new Date().toISOString(),
        user: req.headers['x-user'] || 'system',
        action: '调整库存',
        details: `物品: ${item.name}, 数量: ${oldQuantity} -> ${item.quantity}`,
        ip: req.ip || '127.0.0.1'
    });

    res.json({
        success: true,
        message: '库存调整成功',
        item: item
    });
});

// 删除物品
app.delete('/api/inventory/:id', (req, res) => {
    const itemId = parseInt(req.params.id);
    const itemIndex = inventory.findIndex(i => i.id === itemId);

    if (itemIndex === -1) {
        return res.json({
            success: false,
            message: '物品不存在'
        });
    }

    const deletedItem = inventory[itemIndex];
    inventory.splice(itemIndex, 1);

    // 记录操作日志
    systemLogs.push({
        id: systemLogs.length + 1,
        timestamp: new Date().toISOString(),
        user: req.headers['x-user'] || 'system',
        action: '删除物品',
        details: `删除物品: ${deletedItem.name}`,
        ip: req.ip || '127.0.0.1'
    });

    res.json({
        success: true,
        message: '物品删除成功'
    });
});

// 获取申请列表
app.get('/api/requests', (req, res) => {
    res.json({
        success: true,
        requests: requests.sort((a, b) => new Date(b.date) - new Date(a.date))
    });
});

// 提交申请
app.post('/api/requests', (req, res) => {
    const { item, quantity, purpose, type } = req.body;
    const user = req.headers['x-user'] || 'anonymous';

    const newRequest = {
        id: requests.length + 1,
        user,
        item,
        quantity: parseInt(quantity),
        purpose,
        type: type || 'outbound',
        status: '待审批',
        date: new Date().toISOString(),
        created_at: new Date().toISOString()
    };

    requests.push(newRequest);

    // 记录操作日志
    systemLogs.push({
        id: systemLogs.length + 1,
        timestamp: new Date().toISOString(),
        user: user,
        action: '提交申请',
        details: `申请: ${item} x ${quantity}, 用途: ${purpose}`,
        ip: req.ip || '127.0.0.1'
    });

    res.json({
        success: true,
        message: '申请提交成功',
        request: newRequest
    });
});

// 审批申请
app.put('/api/requests/:id/approve', (req, res) => {
    const requestId = parseInt(req.params.id);
    const request = requests.find(r => r.id === requestId);

    if (!request) {
        return res.json({
            success: false,
            message: '申请不存在'
        });
    }

    if (request.status !== '待审批') {
        return res.json({
            success: false,
            message: '申请状态不正确'
        });
    }

    request.status = '已批准';
    request.approved_at = new Date().toISOString();
    request.approved_by = req.headers['x-user'] || 'system';

    // 如果是出库申请，更新库存
    if (request.type === 'outbound') {
        const item = inventory.find(i => i.name === request.item);
        if (item && item.quantity >= request.quantity) {
            item.quantity -= request.quantity;
        }
    }

    // 记录操作日志
    systemLogs.push({
        id: systemLogs.length + 1,
        timestamp: new Date().toISOString(),
        user: req.headers['x-user'] || 'system',
        action: '批准申请',
        details: `批准申请: ${request.item} x ${request.quantity}`,
        ip: req.ip || '127.0.0.1'
    });

    res.json({
        success: true,
        message: '申请已批准',
        request: request
    });
});

// 拒绝申请
app.put('/api/requests/:id/reject', (req, res) => {
    const requestId = parseInt(req.params.id);
    const { reason } = req.body;
    const request = requests.find(r => r.id === requestId);

    if (!request) {
        return res.json({
            success: false,
            message: '申请不存在'
        });
    }

    if (request.status !== '待审批') {
        return res.json({
            success: false,
            message: '申请状态不正确'
        });
    }

    request.status = '已拒绝';
    request.rejected_at = new Date().toISOString();
    request.rejected_by = req.headers['x-user'] || 'system';
    request.reject_reason = reason;

    // 记录操作日志
    systemLogs.push({
        id: systemLogs.length + 1,
        timestamp: new Date().toISOString(),
        user: req.headers['x-user'] || 'system',
        action: '拒绝申请',
        details: `拒绝申请: ${request.item} x ${request.quantity}, 原因: ${reason}`,
        ip: req.ip || '127.0.0.1'
    });

    res.json({
        success: true,
        message: '申请已拒绝',
        request: request
    });
});

// 处理根路径
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'index.html'));
});

// 处理视图文件
app.get('/views/:filename', (req, res) => {
    const filename = req.params.filename;
    const filePath = path.join(__dirname, 'views', filename);
    
    if (fs.existsSync(filePath)) {
        res.sendFile(filePath);
    } else {
        res.status(404).send('页面未找到');
    }
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`Express服务器运行在 http://localhost:${PORT}`);
    console.log('API端点:');
    console.log('  POST /api/login - 用户登录');
    console.log('  POST /api/register - 用户注册');
    console.log('  GET  /api/users - 获取用户列表');
    console.log('  POST /api/users - 添加用户');
    console.log('  DELETE /api/users/:id - 删除用户');
    console.log('  PUT  /api/users/:id/role - 更新用户角色');
    console.log('  GET  /api/logs - 获取系统日志');
    console.log('  GET  /api/stats - 获取系统统计');
});

module.exports = app;
