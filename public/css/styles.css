/* 仓库管理系统通用样式 */

/* 基础样式 */
body {
    margin: 0;
    padding: 0;
    font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
    /* 现代化渐变背景，替代原来的背景图片 */
    background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
    background-attachment: fixed;
    position: relative;
    min-height: 100vh;
    scroll-behavior: smooth; /* 平滑滚动效果 */
}

/* 背景动画 */
@keyframes gradientBG {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 登录页面样式 */
.container {
    background-color: rgba(255, 255, 255, 0.65);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    padding: 25px;
    width: 340px;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.container:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
}

/* 标题样式 */
h2 {
    text-align: center;
    color: #333;
    margin-bottom: 25px;
    font-size: 28px;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.1);
}

/* 系统标题样式 */
.system-title {
    position: absolute;
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 42px;
    color: #fff;
    text-shadow: 0 0 10px rgba(0,0,0,0.5), 0 0 20px rgba(76, 175, 80, 0.7);
    font-weight: bold;
    letter-spacing: 2px;
    z-index: 10;
    background: rgba(0,0,0,0.3);
    padding: 15px 30px;
    border-radius: 10px;
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    border: 2px solid rgba(255,255,255,0.2);
    animation: titleGlow 2s infinite alternate;
}

@keyframes titleGlow {
    from { box-shadow: 0 0 10px rgba(76, 175, 80, 0.5); }
    to { box-shadow: 0 0 20px rgba(76, 175, 80, 0.8); }
}

/* 表单元素样式 */
input {
    width: 100%;
    padding: 12px;
    box-sizing: border-box;
    border: 1px solid #ddd;
    border-radius: 6px;
    transition: all 0.3s;
    font-size: 16px;
}

input:focus {
    outline: none;
    border-color: #4caf50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

button {
    width: 100%;
    padding: 12px;
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s;
}

button:hover {
    background-color: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 仪表盘通用样式 */
.section { display: none; }
.section.active { display: block; }

/* 导航样式 */
.nav-links a {
    color: white;
    text-decoration: none;
    padding: 6px 12px;
    border-radius: 4px;
    transition: background 0.3s;
}

.nav-links a.active {
    background: rgba(255,255,255,0.3);
}

.nav-links a:hover {
    background: rgba(255,255,255,0.2);
}