// 模拟API服务
// 模拟用户数据
const users = [
    {
        id: 1, // 用户ID
        username: 'admin', // 用户名
        password: 'admin123', // 密码
        role: 'admin', // 角色：管理员
        created_at: '2025-04-21T00:00:00.000Z' // 创建时间
    },
    {
        id: 2, // 用户ID
        username: 'user', // 用户名
        password: 'password', // 密码
        role: 'user', // 角色：普通用户
        created_at: '2025-04-21T00:00:00.000Z' // 创建时间
    }
];

// 模拟库存数据
const inventory = [
    // 模拟库存数据
    {
        id: 1, // 物品ID
        name: '笔记本电脑', // 名称
        description: '高性能笔记本电脑，适合办公和游戏', // 描述
        quantity: 50, // 数量
        price: 5999.99, // 价格
        category: '电子设备', // 分类
        created_at: '2024-01-01T00:00:00.000Z', // 创建时间
        updated_at: '2024-01-01T00:00:00.000Z' // 更新时间
    },
    {
        id: 2, // 物品ID
        name: '办公桌', // 名称
        description: '现代风格办公桌，带抽屉', // 描述
        quantity: 20, // 数量
        price: 799.99, // 价格
        category: '办公家具', // 分类
        created_at: '2024-01-01T00:00:00.000Z', // 创建时间
        updated_at: '2024-01-01T00:00:00.000Z' // 更新时间
    },
    {
        id: 3, // 物品ID
        name: '打印机', // 名称
        description: '彩色激光打印机，高速打印', // 描述
        quantity: 15, // 数量
        price: 1299.99, // 价格
        category: '办公设备', // 分类
        created_at: '2024-01-01T00:00:00.000Z', // 创建时间
        updated_at: '2024-01-01T00:00:00.000Z' // 更新时间
    },
    {
        id: 4, // 物品ID
        name: '办公椅', // 名称
        description: '人体工学办公椅，舒适耐用', // 描述
        quantity: 30, // 数量
        price: 499.99, // 价格
        category: '办公家具', // 分类
        created_at: '2024-01-01T00:00:00.000Z', // 创建时间
        updated_at: '2024-01-01T00:00:00.000Z' // 更新时间
    },
    {
        id: 5, // 物品ID
        name: '文件柜', // 名称
        description: '钢制文件柜，四层带锁', // 描述
        quantity: 10, // 数量
        price: 899.99, // 价格
        category: '办公家具', // 分类
        created_at: '2024-01-01T00:00:00.000Z', // 创建时间
        updated_at: '2024-01-01T00:00:00.000Z' // 更新时间
    }
];

// 模拟登录函数
function mockLogin(username, password, role) {
    // 模拟登录功能
    console.log(`尝试登录: ${username}, ${password}, 角色: ${role}`); // 日志
    
    // 直接查找匹配用户名密码的用户
    const user = users.find(u => u.username === username && u.password === password);
    
    // 返回结果
    if (user) {
        // 找到匹配的用户
        // 如果角色不匹配，返回错误
        if (role && user.role !== role) {
            return {
                success: false, // 失败标志
                message: '所选角色不匹配，请选择正确的角色' // 失败消息
            };
        }
        
        // 用户名密码和角色都匹配
        return {
            success: true, // 成功标志
            message: '登录成功', // 成功消息
            user: {
                id: user.id, // 用户ID
                username: user.username, // 用户名
                role: user.role, // 角色
                created_at: user.created_at // 创建时间
            }
        };
    } else {
        // 未找到匹配的用户
        return {
            success: false, // 失败标志
            message: '用户名或密码错误' // 失败消息
        };
    }
}

// 模拟注册
function mockRegister(username, password, role) {
    // 模拟注册功能
    console.log(`尝试注册: ${username}, ${role}`); // 日志
    
    // 检查用户名是否已存在
    if (users.some(u => u.username === username)) {
        // 用户名已存在
        return {
            success: false, // 失败标志
            message: '用户名已存在' // 失败消息
        };
    }
    
    // 创建新用户
    const newUser = {
        id: users.length + 1, // 新的用户ID
        username: username, // 用户名
        password: password, // 密码
        role: role || 'user', // 角色，默认为普通用户
        created_at: new Date().toISOString() // 创建时间为当前时间
    };
    
    // 添加到用户列表
    users.push(newUser);
    
    // 返回成功结果
    return {
        success: true, // 成功标志
        message: '注册成功，请登录', // 成功消息
        user: {
            id: newUser.id, // 用户ID
            username: newUser.username, // 用户名
            role: newUser.role, // 角色
            created_at: newUser.created_at // 创建时间
        }
    };
}

// 模拟获取库存数据
function mockGetInventory() {
    // 模拟获取库存数据
    return {
        success: true, // 成功标志
        items: inventory // 库存数据
    };
}

// 模拟搜索库存
function mockSearchInventory(keyword) {
    // 模拟搜索库存功能
    if (!keyword) {
        // 如果关键词为空，返回所有库存
        return mockGetInventory();
    }
    
    // 根据关键词过滤物品
    const filteredItems = inventory.filter(item => 
        item.name.toLowerCase().includes(keyword.toLowerCase()) || 
        (item.description && item.description.toLowerCase().includes(keyword.toLowerCase())) ||
        (item.category && item.category.toLowerCase().includes(keyword.toLowerCase()))
    );
    
    // 返回过滤结果
    return {
        success: true, // 成功标志
        items: filteredItems // 过滤后的物品
    };
}

// 模拟添加物品
function mockAddItem(item) {
    // 模拟添加物品功能
    const newItem = {
        ...item, // 展开传入的物品属性
        id: inventory.length + 1, // 新的物品ID
        created_at: new Date().toISOString(), // 创建时间为当前时间
        updated_at: new Date().toISOString() // 更新时间为当前时间
    };
    
    // 添加到库存
    inventory.push(newItem);
    
    // 返回成功结果
    return {
        success: true, // 成功标志
        message: '物品添加成功', // 成功消息
        item: newItem // 添加的物品
    };
}

// 模拟更新物品
function mockUpdateItem(id, updates) {
    // 模拟更新物品功能
    const index = inventory.findIndex(item => item.id === id); // 查找物品索引
    
    // 检查物品是否存在
    if (index === -1) {
        // 物品不存在
        return {
            success: false, // 失败标志
            message: '物品不存在' // 失败消息
        };
    }
    
    // 更新物品
    inventory[index] = {
        ...inventory[index], // 保留原有属性
        ...updates, // 应用更新
        updated_at: new Date().toISOString() // 更新时间为当前时间
    };
    
    // 返回成功结果
    return {
        success: true, // 成功标志
        message: '物品更新成功', // 成功消息
        item: inventory[index] // 更新后的物品
    };
}

// 模拟删除物品
function mockDeleteItem(id) {
    // 模拟删除物品功能
    const index = inventory.findIndex(item => item.id === id); // 查找物品索引
    
    // 检查物品是否存在
    if (index === -1) {
        // 物品不存在
        return {
            success: false, // 失败标志
            message: '物品不存在' // 失败消息
        };
    }
    
    // 删除物品
    inventory.splice(index, 1);
    
    // 返回成功结果
    return {
        success: true, // 成功标志
        message: '物品删除成功' // 成功消息
    };
}

// 模拟申请记录数组 - 使用localStorage持久化存储
// 从localStorage获取已有的申请记录，如果没有则初始化为空数组
let requests = JSON.parse(localStorage.getItem('requests') || '[]'); // 存储用户申请记录

// 模拟提交申请功能
function mockSubmitRequest(userId, itemId, quantity, reason) {
    // 生成申请ID，格式 REQ-0001
    const requestId = 'REQ-' + String(requests.length + 1).padStart(4, '0');
    // 获取当前日期 YYYY-MM-DD
    const date = new Date().toISOString().split('T')[0];
    // 构造新申请对象
    const newReq = { id: requestId, userId, itemId, quantity, reason, date, status: '待审批' };
    // 存入请求列表
    requests.push(newReq);
    // 保存到localStorage
    localStorage.setItem('requests', JSON.stringify(requests));
    // 返回结果
    return { success: true, message: '申请提交成功', request: newReq };
}

// 模拟获取用户申请记录
function mockGetRequests(userId) {
    // 从localStorage获取最新的申请记录
    requests = JSON.parse(localStorage.getItem('requests') || '[]');
    // 筛选当前用户的所有申请
    const userReqs = requests.filter(r => r.userId === userId);
    return { success: true, requests: userReqs };
}

// 模拟获取所有申请记录
function mockGetAllRequests() {
    // 从localStorage获取最新的申请记录
    requests = JSON.parse(localStorage.getItem('requests') || '[]');
    // 返回全部申请记录
    return { success: true, requests: requests };
}

// 模拟更新申请状态
function mockUpdateRequestStatus(requestId, status) {
    // 从localStorage获取最新的申请记录
    requests = JSON.parse(localStorage.getItem('requests') || '[]');
    // 查找申请索引
    const idx = requests.findIndex(r => r.id === requestId);
    if (idx === -1) {
        return { success: false, message: '申请记录不存在' };
    }
    // 更新状态
    requests[idx].status = status;
    // 保存到localStorage
    localStorage.setItem('requests', JSON.stringify(requests));
    return { success: true, message: '更新成功', request: requests[idx] };
}

// 物品使用记录数组 - 使用localStorage持久化存储
let usageRecords = JSON.parse(localStorage.getItem('usageRecords') || '[]');

// 模拟记录物品使用情况
function mockRecordItemUsage(userId, itemId, quantity, purpose) {
    // 获取物品信息
    const item = inventory.find(i => i.id === itemId);
    if (!item) {
        return { success: false, message: '物品不存在' };
    }
    
    // 生成使用记录ID
    const usageId = 'USAGE-' + String(usageRecords.length + 1).padStart(4, '0');
    // 获取当前日期时间
    const timestamp = new Date().toISOString();
    // 构造新使用记录
    const newUsage = { 
        id: usageId, 
        userId, 
        itemId, 
        itemName: item.name,
        quantity, 
        purpose, 
        timestamp,
        category: item.category || '未分类'
    };
    
    // 存入使用记录列表
    usageRecords.push(newUsage);
    // 保存到localStorage
    localStorage.setItem('usageRecords', JSON.stringify(usageRecords));
    
    return { success: true, message: '使用记录已添加', usage: newUsage };
}

// 模拟获取用户物品使用记录
function mockGetUserUsageRecords(userId) {
    // 从localStorage获取最新的使用记录
    usageRecords = JSON.parse(localStorage.getItem('usageRecords') || '[]');
    // 筛选当前用户的所有使用记录
    const userUsages = usageRecords.filter(r => r.userId === userId);
    return { success: true, usages: userUsages };
}

// 模拟获取所有物品使用记录
function mockGetAllUsageRecords() {
    // 从localStorage获取最新的使用记录
    usageRecords = JSON.parse(localStorage.getItem('usageRecords') || '[]');
    return { success: true, usages: usageRecords };
}

// 模拟获取物品使用统计
function mockGetUsageStatistics() {
    // 从localStorage获取最新的使用记录
    usageRecords = JSON.parse(localStorage.getItem('usageRecords') || '[]');
    
    // 按物品ID分组统计
    const itemStats = {};
    usageRecords.forEach(record => {
        if (!itemStats[record.itemId]) {
            itemStats[record.itemId] = {
                itemId: record.itemId,
                itemName: record.itemName,
                totalUsage: 0,
                usageCount: 0,
                category: record.category
            };
        }
        itemStats[record.itemId].totalUsage += record.quantity;
        itemStats[record.itemId].usageCount += 1;
    });
    
    // 转换为数组
    const statistics = Object.values(itemStats);
    
    return { success: true, statistics };
}

// 模拟获取库存预警信息
function mockGetInventoryAlerts() {
    // 设置预警阈值
    const lowStockThreshold = 20; // 库存低于此值时发出预警
    
    // 筛选低库存物品
    const lowStockItems = inventory.filter(item => item.quantity < lowStockThreshold);
    
    // 计算库存使用率
    const usageStats = mockGetUsageStatistics().statistics;
    const alerts = [];
    
    // 生成预警信息
    lowStockItems.forEach(item => {
        const usage = usageStats.find(stat => stat.itemId === item.id);
        const alert = {
            itemId: item.id,
            name: item.name,
            currentStock: item.quantity,
            threshold: lowStockThreshold,
            category: item.category,
            usageRate: usage ? usage.totalUsage : 0,
            severity: item.quantity < 5 ? 'high' : 'medium',
            recommendation: item.quantity < 5 ? '立即补充库存' : '考虑补充库存'
        };
        alerts.push(alert);
    });
    
    return { success: true, alerts };
}