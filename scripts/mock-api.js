// 模拟API服务
// 模拟用户数据
const users = [
    {
        id: 1, // 用户ID
        username: 'sysadmin', // 用户名
        password: 'sys123', // 密码
        role: 'system_admin', // 角色：系统管理员
        created_at: '2025-04-21T00:00:00.000Z' // 创建时间
    },
    {
        id: 2, // 用户ID
        username: 'bizadmin', // 用户名
        password: 'biz123', // 密码
        role: 'business_admin', // 角色：业务管理员
        created_at: '2025-04-21T00:00:00.000Z' // 创建时间
    },
    {
        id: 3, // 用户ID
        username: 'user', // 用户名
        password: 'password', // 密码
        role: 'user', // 角色：普通用户
        created_at: '2025-04-21T00:00:00.000Z' // 创建时间
    }
];

// 模拟库存数据
const inventory = [
    // 模拟库存数据
    {
        id: 1, // 物品ID
        name: '笔记本电脑', // 名称
        description: '高性能笔记本电脑，适合办公和游戏', // 描述
        quantity: 50, // 数量
        price: 5999.99, // 价格
        category: '电子设备', // 分类
        created_at: '2024-01-01T00:00:00.000Z', // 创建时间
        updated_at: '2024-01-01T00:00:00.000Z' // 更新时间
    },
    {
        id: 2, // 物品ID
        name: '办公桌', // 名称
        description: '现代风格办公桌，带抽屉', // 描述
        quantity: 20, // 数量
        price: 799.99, // 价格
        category: '办公家具', // 分类
        created_at: '2024-01-01T00:00:00.000Z', // 创建时间
        updated_at: '2024-01-01T00:00:00.000Z' // 更新时间
    },
    {
        id: 3, // 物品ID
        name: '打印机', // 名称
        description: '彩色激光打印机，高速打印', // 描述
        quantity: 15, // 数量
        price: 1299.99, // 价格
        category: '办公设备', // 分类
        created_at: '2024-01-01T00:00:00.000Z', // 创建时间
        updated_at: '2024-01-01T00:00:00.000Z' // 更新时间
    },
    {
        id: 4, // 物品ID
        name: '办公椅', // 名称
        description: '人体工学办公椅，舒适耐用', // 描述
        quantity: 30, // 数量
        price: 499.99, // 价格
        category: '办公家具', // 分类
        created_at: '2024-01-01T00:00:00.000Z', // 创建时间
        updated_at: '2024-01-01T00:00:00.000Z' // 更新时间
    },
    {
        id: 5, // 物品ID
        name: '文件柜', // 名称
        description: '钢制文件柜，四层带锁', // 描述
        quantity: 10, // 数量
        price: 899.99, // 价格
        category: '办公家具', // 分类
        created_at: '2024-01-01T00:00:00.000Z', // 创建时间
        updated_at: '2024-01-01T00:00:00.000Z' // 更新时间
    }
];

// 模拟登录函数
function mockLogin(username, password, role) {
    // 模拟登录功能
    console.log(`尝试登录: ${username}, ${password}, 角色: ${role}`); // 日志
    
    // 直接查找匹配用户名密码的用户
    const user = users.find(u => u.username === username && u.password === password);
    
    // 返回结果
    if (user) {
        // 找到匹配的用户
        // 如果角色不匹配，返回错误
        if (role && user.role !== role) {
            return {
                success: false, // 失败标志
                message: '所选角色不匹配，请选择正确的角色' // 失败消息
            };
        }
        
        // 用户名密码和角色都匹配
        return {
            success: true, // 成功标志
            message: '登录成功', // 成功消息
            user: {
                id: user.id, // 用户ID
                username: user.username, // 用户名
                role: user.role, // 角色
                created_at: user.created_at // 创建时间
            }
        };
    } else {
        // 未找到匹配的用户
        return {
            success: false, // 失败标志
            message: '用户名或密码错误' // 失败消息
        };
    }
}

// 模拟注册
function mockRegister(username, password, role) {
    // 模拟注册功能
    console.log(`尝试注册: ${username}, ${role}`); // 日志
    
    // 检查用户名是否已存在
    if (users.some(u => u.username === username)) {
        // 用户名已存在
        return {
            success: false, // 失败标志
            message: '用户名已存在' // 失败消息
        };
    }
    
    // 创建新用户
    const newUser = {
        id: users.length + 1, // 新的用户ID
        username: username, // 用户名
        password: password, // 密码
        role: role || 'user', // 角色，默认为普通用户
        created_at: new Date().toISOString() // 创建时间为当前时间
    };
    
    // 添加到用户列表
    users.push(newUser);
    
    // 返回成功结果
    return {
        success: true, // 成功标志
        message: '注册成功，请登录', // 成功消息
        user: {
            id: newUser.id, // 用户ID
            username: newUser.username, // 用户名
            role: newUser.role, // 角色
            created_at: newUser.created_at // 创建时间
        }
    };
}

// 模拟获取库存数据
function mockGetInventory() {
    // 模拟获取库存数据
    return {
        success: true, // 成功标志
        items: inventory // 库存数据
    };
}

// 模拟搜索库存
function mockSearchInventory(keyword) {
    // 模拟搜索库存功能
    if (!keyword) {
        // 如果关键词为空，返回所有库存
        return mockGetInventory();
    }
    
    // 根据关键词过滤物品
    const filteredItems = inventory.filter(item => 
        item.name.toLowerCase().includes(keyword.toLowerCase()) || 
        (item.description && item.description.toLowerCase().includes(keyword.toLowerCase())) ||
        (item.category && item.category.toLowerCase().includes(keyword.toLowerCase()))
    );
    
    // 返回过滤结果
    return {
        success: true, // 成功标志
        items: filteredItems // 过滤后的物品
    };
}

// 模拟添加物品
function mockAddItem(item) {
    // 模拟添加物品功能
    const newItem = {
        ...item, // 展开传入的物品属性
        id: inventory.length + 1, // 新的物品ID
        created_at: new Date().toISOString(), // 创建时间为当前时间
        updated_at: new Date().toISOString() // 更新时间为当前时间
    };
    
    // 添加到库存
    inventory.push(newItem);
    
    // 返回成功结果
    return {
        success: true, // 成功标志
        message: '物品添加成功', // 成功消息
        item: newItem // 添加的物品
    };
}

// 模拟更新物品
function mockUpdateItem(id, updates) {
    // 模拟更新物品功能
    const index = inventory.findIndex(item => item.id === id); // 查找物品索引
    
    // 检查物品是否存在
    if (index === -1) {
        // 物品不存在
        return {
            success: false, // 失败标志
            message: '物品不存在' // 失败消息
        };
    }
    
    // 更新物品
    inventory[index] = {
        ...inventory[index], // 保留原有属性
        ...updates, // 应用更新
        updated_at: new Date().toISOString() // 更新时间为当前时间
    };
    
    // 返回成功结果
    return {
        success: true, // 成功标志
        message: '物品更新成功', // 成功消息
        item: inventory[index] // 更新后的物品
    };
}

// 模拟删除物品
function mockDeleteItem(id) {
    // 模拟删除物品功能
    const index = inventory.findIndex(item => item.id === id); // 查找物品索引
    
    // 检查物品是否存在
    if (index === -1) {
        // 物品不存在
        return {
            success: false, // 失败标志
            message: '物品不存在' // 失败消息
        };
    }
    
    // 删除物品
    inventory.splice(index, 1);
    
    // 返回成功结果
    return {
        success: true, // 成功标志
        message: '物品删除成功' // 成功消息
    };
}

// 模拟申请记录数组 - 使用localStorage持久化存储
// 从localStorage获取已有的申请记录，如果没有则初始化为空数组
let requests = JSON.parse(localStorage.getItem('requests') || '[]'); // 存储用户申请记录

// 模拟提交申请功能
function mockSubmitRequest(userId, itemId, quantity, reason) {
    // 生成申请ID，格式 REQ-0001
    const requestId = 'REQ-' + String(requests.length + 1).padStart(4, '0');
    // 获取当前日期 YYYY-MM-DD
    const date = new Date().toISOString().split('T')[0];
    // 构造新申请对象
    const newReq = { id: requestId, userId, itemId, quantity, reason, date, status: '待审批' };
    // 存入请求列表
    requests.push(newReq);
    // 保存到localStorage
    localStorage.setItem('requests', JSON.stringify(requests));
    // 返回结果
    return { success: true, message: '申请提交成功', request: newReq };
}

// 模拟获取用户申请记录
function mockGetRequests(userId) {
    // 从localStorage获取最新的申请记录
    requests = JSON.parse(localStorage.getItem('requests') || '[]');
    // 筛选当前用户的所有申请
    const userReqs = requests.filter(r => r.userId === userId);
    return { success: true, requests: userReqs };
}

// 模拟获取所有申请记录
function mockGetAllRequests() {
    // 从localStorage获取最新的申请记录
    requests = JSON.parse(localStorage.getItem('requests') || '[]');
    // 返回全部申请记录
    return { success: true, requests: requests };
}

// 模拟更新申请状态
function mockUpdateRequestStatus(requestId, status) {
    // 从localStorage获取最新的申请记录
    requests = JSON.parse(localStorage.getItem('requests') || '[]');
    // 查找申请索引
    const idx = requests.findIndex(r => r.id === requestId);
    if (idx === -1) {
        return { success: false, message: '申请记录不存在' };
    }
    // 更新状态
    requests[idx].status = status;
    // 保存到localStorage
    localStorage.setItem('requests', JSON.stringify(requests));
    return { success: true, message: '更新成功', request: requests[idx] };
}

// 模拟获取库存预警数据
function mockGetInventoryWarnings(threshold = 10) {
    // 获取低于阈值的物品
    const lowStockItems = inventory.filter(item => item.quantity <= threshold);
    
    // 按库存量升序排序
    lowStockItems.sort((a, b) => a.quantity - b.quantity);
    
    return {
        success: true,
        items: lowStockItems,
        threshold: threshold
    };
}

// 模拟更新预警阈值
function mockUpdateWarningThreshold(threshold) {
    // 在实际应用中，这会保存到数据库或配置中
    // 这里我们使用localStorage模拟持久化
    localStorage.setItem('warningThreshold', threshold);
    return {
        success: true,
        message: '预警阈值更新成功',
        threshold: threshold
    };
}

// 模拟获取当前预警阈值
function mockGetWarningThreshold() {
    // 从localStorage获取，如果不存在则默认为10
    const threshold = parseInt(localStorage.getItem('warningThreshold') || '10');
    return {
        success: true,
        threshold: threshold
    };
}

// 模拟获取用户使用统计数据
function mockGetUsageData(userId) {
    // 模拟使用记录数据
    const records = [
        { date: '2024-05-01', itemName: '笔记本电脑', quantity: 2, purpose: '项目开发' },
        { date: '2024-04-25', itemName: '打印机', quantity: 1, purpose: '文档打印' },
        { date: '2024-04-20', itemName: '办公椅', quantity: 3, purpose: '会议室布置' },
        { date: '2024-04-15', itemName: '文件柜', quantity: 1, purpose: '文档存储' },
        { date: '2024-04-10', itemName: '笔记本电脑', quantity: 1, purpose: '新员工配置' },
        { date: '2024-04-05', itemName: '办公桌', quantity: 2, purpose: '办公室扩展' }
    ];

    // 模拟使用趋势数据（近6个月）
    const trends = [
        { month: '2023-12', count: 5 },
        { month: '2024-01', count: 8 },
        { month: '2024-02', count: 12 },
        { month: '2024-03', count: 7 },
        { month: '2024-04', count: 15 },
        { month: '2024-05', count: 10 }
    ];

    // 模拟物品分类使用统计
    const categories = [
        { name: '电子设备', count: 25 },
        { name: '办公家具', count: 18 },
        { name: '办公用品', count: 30 },
        { name: '办公设备', count: 12 },
        { name: '其他', count: 5 }
    ];

    return {
        success: true,
        records: records,
        trends: trends,
        categories: categories
    };
}

// 模拟获取所有用户数据（系统管理员功能）
function mockGetAllUsers() {
    return {
        success: true,
        users: users.map(user => ({
            id: user.id,
            username: user.username,
            role: user.role,
            created_at: user.created_at
        }))
    };
}

// 模拟添加用户（系统管理员功能）
function mockAddUser(userData) {
    // 检查用户名是否已存在
    if (users.some(u => u.username === userData.username)) {
        return {
            success: false,
            message: '用户名已存在'
        };
    }

    const newUser = {
        id: users.length + 1,
        username: userData.username,
        password: userData.password,
        role: userData.role,
        created_at: new Date().toISOString()
    };

    users.push(newUser);

    return {
        success: true,
        message: '用户添加成功',
        user: {
            id: newUser.id,
            username: newUser.username,
            role: newUser.role,
            created_at: newUser.created_at
        }
    };
}

// 模拟删除用户（系统管理员功能）
function mockDeleteUser(userId) {
    const index = users.findIndex(user => user.id === userId);

    if (index === -1) {
        return {
            success: false,
            message: '用户不存在'
        };
    }

    // 不能删除自己
    if (userId === 1) {
        return {
            success: false,
            message: '不能删除系统管理员账号'
        };
    }

    users.splice(index, 1);

    return {
        success: true,
        message: '用户删除成功'
    };
}

// 模拟更新用户角色（系统管理员功能）
function mockUpdateUserRole(userId, newRole) {
    const user = users.find(u => u.id === userId);

    if (!user) {
        return {
            success: false,
            message: '用户不存在'
        };
    }

    // 不能修改系统管理员的角色
    if (userId === 1) {
        return {
            success: false,
            message: '不能修改系统管理员的角色'
        };
    }

    user.role = newRole;

    return {
        success: true,
        message: '用户角色更新成功',
        user: {
            id: user.id,
            username: user.username,
            role: user.role,
            created_at: user.created_at
        }
    };
}

// 模拟获取系统日志（系统管理员功能）
function mockGetSystemLogs() {
    const logs = [
        { id: 1, timestamp: '2024-05-01 10:30:00', user: 'bizadmin', action: '添加物品', details: '添加了笔记本电脑' },
        { id: 2, timestamp: '2024-05-01 09:15:00', user: 'user', action: '提交申请', details: '申请办公椅 x2' },
        { id: 3, timestamp: '2024-04-30 16:45:00', user: 'bizadmin', action: '审批申请', details: '批准了用户的申请' },
        { id: 4, timestamp: '2024-04-30 14:20:00', user: 'sysadmin', action: '用户管理', details: '添加了新用户' },
        { id: 5, timestamp: '2024-04-30 11:10:00', user: 'bizadmin', action: '库存更新', details: '更新了打印机库存' }
    ];

    return {
        success: true,
        logs: logs
    };
}

// 模拟获取系统统计数据（系统管理员功能）
function mockGetSystemStats() {
    return {
        success: true,
        stats: {
            totalUsers: users.length,
            totalItems: inventory.length,
            totalRequests: requests.length,
            pendingRequests: requests.filter(r => r.status === '待审批').length,
            lowStockItems: inventory.filter(item => item.quantity <= 10).length,
            systemUptime: '15天 8小时 32分钟'
        }
    };
}

// 模拟批准申请（业务管理员功能）
function mockApproveRequest(requestId) {
    // 从localStorage获取最新的申请记录
    requests = JSON.parse(localStorage.getItem('requests') || '[]');

    const index = requests.findIndex(r => r.id === requestId);

    if (index === -1) {
        return {
            success: false,
            message: '申请不存在'
        };
    }

    if (requests[index].status !== '待审批') {
        return {
            success: false,
            message: '申请状态不正确'
        };
    }

    requests[index].status = '已批准';
    requests[index].approvedAt = new Date().toISOString();

    // 保存到localStorage
    localStorage.setItem('requests', JSON.stringify(requests));

    return {
        success: true,
        message: '申请已批准'
    };
}

// 模拟拒绝申请（业务管理员功能）
function mockRejectRequest(requestId, reason) {
    // 从localStorage获取最新的申请记录
    requests = JSON.parse(localStorage.getItem('requests') || '[]');

    const index = requests.findIndex(r => r.id === requestId);

    if (index === -1) {
        return {
            success: false,
            message: '申请不存在'
        };
    }

    if (requests[index].status !== '待审批') {
        return {
            success: false,
            message: '申请状态不正确'
        };
    }

    requests[index].status = '已拒绝';
    requests[index].rejectedAt = new Date().toISOString();
    requests[index].rejectReason = reason;

    // 保存到localStorage
    localStorage.setItem('requests', JSON.stringify(requests));

    return {
        success: true,
        message: '申请已拒绝'
    };
}

// 模拟获取业务统计数据（业务管理员功能）
function mockGetBusinessStats() {
    return {
        success: true,
        stats: {
            totalItems: inventory.length,
            lowStockItems: inventory.filter(item => item.quantity <= 10).length,
            pendingRequests: requests.filter(r => r.status === '待审批').length,
            todayTransactions: Math.floor(Math.random() * 50) + 10 // 模拟今日交易数
        }
    };
}