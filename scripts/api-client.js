// API客户端 - 与Express服务器通信
class ApiClient {
    constructor() {
        this.baseURL = 'http://localhost:3000/api';
        this.currentUser = this.getCurrentUser();
    }

    // 获取当前用户信息
    getCurrentUser() {
        const userStr = localStorage.getItem('user');
        return userStr ? JSON.parse(userStr) : null;
    }

    // 设置请求头
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (this.currentUser) {
            headers['x-user'] = this.currentUser.username;
        }
        
        return headers;
    }

    // 通用请求方法
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: this.getHeaders(),
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('API请求错误:', error);
            return {
                success: false,
                message: '网络请求失败'
            };
        }
    }

    // 用户登录
    async login(username, password, role) {
        const result = await this.request('/login', {
            method: 'POST',
            body: JSON.stringify({ username, password, role })
        });

        if (result.success) {
            localStorage.setItem('user', JSON.stringify(result.user));
            this.currentUser = result.user;
        }

        return result;
    }

    // 用户注册
    async register(username, password) {
        return await this.request('/register', {
            method: 'POST',
            body: JSON.stringify({ username, password })
        });
    }

    // 获取所有用户
    async getAllUsers() {
        return await this.request('/users');
    }

    // 添加用户
    async addUser(userData) {
        return await this.request('/users', {
            method: 'POST',
            body: JSON.stringify(userData)
        });
    }

    // 删除用户
    async deleteUser(userId) {
        return await this.request(`/users/${userId}`, {
            method: 'DELETE'
        });
    }

    // 更新用户角色
    async updateUserRole(userId, role) {
        return await this.request(`/users/${userId}/role`, {
            method: 'PUT',
            body: JSON.stringify({ role })
        });
    }

    // 获取系统日志
    async getSystemLogs() {
        return await this.request('/logs');
    }

    // 获取系统统计
    async getSystemStats() {
        return await this.request('/stats');
    }

    // 获取库存列表
    async getInventory() {
        return await this.request('/inventory');
    }

    // 添加物品
    async addItem(itemData) {
        return await this.request('/inventory', {
            method: 'POST',
            body: JSON.stringify(itemData)
        });
    }

    // 更新库存数量
    async updateItemQuantity(itemId, quantity) {
        return await this.request(`/inventory/${itemId}/quantity`, {
            method: 'PUT',
            body: JSON.stringify({ quantity })
        });
    }

    // 删除物品
    async deleteItem(itemId) {
        return await this.request(`/inventory/${itemId}`, {
            method: 'DELETE'
        });
    }

    // 获取申请列表
    async getRequests() {
        return await this.request('/requests');
    }

    // 提交申请
    async submitRequest(requestData) {
        return await this.request('/requests', {
            method: 'POST',
            body: JSON.stringify(requestData)
        });
    }

    // 批准申请
    async approveRequest(requestId) {
        return await this.request(`/requests/${requestId}/approve`, {
            method: 'PUT'
        });
    }

    // 拒绝申请
    async rejectRequest(requestId, reason) {
        return await this.request(`/requests/${requestId}/reject`, {
            method: 'PUT',
            body: JSON.stringify({ reason })
        });
    }
}

// 创建全局API客户端实例
const apiClient = new ApiClient();

// 兼容性函数 - 保持与原有mock-api.js的接口一致
function mockLogin(username, password, role) {
    return apiClient.login(username, password, role);
}

function mockRegister(username, password, role) {
    return apiClient.register(username, password);
}

function mockGetAllUsers() {
    return apiClient.getAllUsers();
}

function mockAddUser(userData) {
    return apiClient.addUser(userData);
}

function mockDeleteUser(userId) {
    return apiClient.deleteUser(userId);
}

function mockUpdateUserRole(userId, role) {
    return apiClient.updateUserRole(userId, role);
}

function mockGetSystemLogs() {
    return apiClient.getSystemLogs();
}

function mockGetSystemStats() {
    return apiClient.getSystemStats();
}

function mockGetInventory() {
    return apiClient.getInventory();
}

function mockAddItem(itemData) {
    return apiClient.addItem(itemData);
}

function mockUpdateItemQuantity(itemId, quantity) {
    return apiClient.updateItemQuantity(itemId, quantity);
}

function mockDeleteItem(itemId) {
    return apiClient.deleteItem(itemId);
}

function mockGetRequests() {
    return apiClient.getRequests();
}

function mockSubmitRequest(requestData) {
    return apiClient.submitRequest(requestData);
}

function mockApproveRequest(requestId) {
    return apiClient.approveRequest(requestId);
}

function mockRejectRequest(requestId, reason) {
    return apiClient.rejectRequest(requestId, reason);
}

// 数据同步功能
class DataSync {
    constructor() {
        this.syncInterval = 30000; // 30秒同步一次
        this.isOnline = navigator.onLine;
        this.setupEventListeners();
    }

    setupEventListeners() {
        // 监听网络状态变化
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.syncData();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
        });

        // 定期同步数据
        setInterval(() => {
            if (this.isOnline) {
                this.syncData();
            }
        }, this.syncInterval);
    }

    async syncData() {
        try {
            // 同步库存数据
            const inventoryResult = await apiClient.getInventory();
            if (inventoryResult.success) {
                localStorage.setItem('inventory_cache', JSON.stringify(inventoryResult.items));
            }

            // 同步申请数据
            const requestsResult = await apiClient.getRequests();
            if (requestsResult.success) {
                localStorage.setItem('requests_cache', JSON.stringify(requestsResult.requests));
            }

            // 触发数据更新事件
            window.dispatchEvent(new CustomEvent('dataSync', {
                detail: {
                    inventory: inventoryResult.items,
                    requests: requestsResult.requests
                }
            }));

        } catch (error) {
            console.error('数据同步失败:', error);
        }
    }

    // 获取缓存数据
    getCachedData(type) {
        const cached = localStorage.getItem(`${type}_cache`);
        return cached ? JSON.parse(cached) : [];
    }
}

// 创建数据同步实例
const dataSync = new DataSync();

// 导出API客户端和数据同步实例
window.apiClient = apiClient;
window.dataSync = dataSync;
