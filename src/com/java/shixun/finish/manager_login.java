package com.java.shixun.finish;

import com.java.shixun.VerificationCode;
import com.sun.org.apache.xpath.internal.operations.And;

import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.Statement;
import javax.swing.*;
import java.sql.*;

public class manager_login extends J<PERSON>rame {
private VerificationCode vcode = new VerificationCode();
JTextField co;
JTextField user;
JPasswordField pass;
JButton ok;
public manager_login() {
    super("�ܹܵ�¼");
    setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
    this.setSize(450,350);
    this.setLocationRelativeTo(null);
    setVisible(true);

    Image img = Toolkit.getDefaultToolkit().getImage("pic/finish/login.png");
    ImagePanel frame = new ImagePanel(img);
    this.add(frame);

    Icon login = new ImageIcon("pic/finish/user.jpg");
    JLabel I = new JLabel();
    I.setIcon(login);

    Icon password = new ImageIcon("pic/finish/password.png");
    JLabel P = new JLabel();
    P.setIcon(password);

    JLabel code = new JLabel("��֤��");
    code.setForeground(Color.white);
    code.setFont(new Font("����",Font.BOLD,17));

    user = new JTextField();
    pass = new JPasswordField();
    co = new JTextField();
    ok = new JButton("��¼");
    ok.setForeground(Color.white);
    ok.setContentAreaFilled(false);

    I.setBounds(80 ,50,60,40);
    P.setBounds(80,100,60,40);
    code.setBounds(70,150,60,40);
    user.setBounds(150,50,150,30);
    pass.setBounds(150,100,150,30);
    co.setBounds(150,150,150,30);
    ok.setBounds(180,220,70,30);
    vcode.setBounds(310,145,100,40);

    frame.setLayout(null);
    frame.add(I);
    frame.add(P);
    frame.add(code);
    frame.add(user);
    frame.add(pass);
    frame.add(co);
    frame.add(ok);
    frame.add(vcode);

    ok.addActionListener(new ActionListener() {
        public void actionPerformed(ActionEvent e) {
            String jusername = user.getText();
            char s[] = pass.getPassword();
            String jpassword = new String(s);
            String coo = co.getText();
            try {
                String user = "sa";
                String password = "123123";
                Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                String url = "******************************************************************************";

                Connection conn = DriverManager.getConnection(url, user, password);
                Statement st = conn.createStatement();
                String  strSQL="(Select * from  dbo.manager where Mname='"+jusername+"'And Mpassword='"+jpassword+"' )";
                ResultSet rs=st.executeQuery(strSQL);
                if (coo.isEmpty()) {
                    JOptionPane.showMessageDialog(null, "��������֤�룡", "��ʾ��Ϣ", JOptionPane.WARNING_MESSAGE);
                } else {
                    if (!isValidCodeRgiht()) {
                        JOptionPane.showMessageDialog(null, "��֤�����", "��ʾ��Ϣ", JOptionPane.WARNING_MESSAGE);
                    } else {
                        if (rs.next()) {
                            JOptionPane.showMessageDialog(null, "��¼�ɹ�!", "��ʾ��Ϣ", JOptionPane.WARNING_MESSAGE);
                            new manager_frame();
                            closeThis();
                        } else {
                            JOptionPane.showMessageDialog(null, "�û��������ڻ��������", "����", JOptionPane.ERROR_MESSAGE);
                        }
                        conn.close();
                    }
                }
            } catch (ClassNotFoundException ex) {
                ex.printStackTrace();
                JOptionPane.showMessageDialog(null, "û���ҵ���Ӧ�����ݿ�����", "��ʾ", JOptionPane.WARNING_MESSAGE);
            } catch (SQLException ex) {
                ex.printStackTrace();
                JOptionPane.showMessageDialog(null, "���ݿ����ӻ��������ݿ����ʧ��", "��ʾ", JOptionPane.WARNING_MESSAGE);
            }
        }

    });

    }
public boolean isValidCodeRgiht() {
    if(co == null){
        return false;
    } else if (vcode == null) {
        return true;
    } else if (vcode.getCode().equals(co.getText())) {
        return true;

    }else{
        return false;
         }
    }
    public void closeThis() {
    this.dispose();
    }

}
