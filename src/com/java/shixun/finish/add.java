package com.java.shixun.finish;

import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JTextField;
public class add extends J<PERSON>rame{
    /**
     *
     */
    //private static final long serialVersionUID = 1L;

    public add() {
        super("��ӻ���");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        this.setSize(500,550);
        this.setLocationRelativeTo(null);
        setVisible(true);

        Image img = Toolkit.getDefaultToolkit().getImage("pic\\finish\\add.png");
        // ����ImagePanel����ӵ�JFrame
        ImagePanel frame = new ImagePanel(img);
        this.add(frame);

        JLabel j=new JLabel("��Ʒ��:"); j.setFont(new Font("����",Font.PLAIN,20));j.setForeground(Color.white);
        JLabel j1=new JLabel("����:");j1.setFont(new Font("����",Font.PLAIN,20));j1.setForeground(Color.white);
        JLabel j2=new JLabel("�ֿ���:");j2.setFont(new Font("����",Font.PLAIN,20));j2.setForeground(Color.white);
        JLabel j3=new JLabel("���ܺ�:");j3.setFont(new Font("����",Font.PLAIN,20));j3.setForeground(Color.white);

        JLabel j8=new JLabel("(��ܰ���ѣ�Ҫ����Ϣ��ȫŶ��)");
        j8.setForeground(Color.white);
        j8.setFont(new Font("����",Font.PLAIN,11));

        JButton aa=new JButton("ȷ��");
        aa.setFont(new Font("����",Font.PLAIN,20));
        aa.setBackground(Color.GREEN);
        JButton bb=new JButton("����");
        bb.setFont(new Font("����",Font.PLAIN,20));
        bb.setBackground(Color.RED);

        JTextField c=new JTextField(15);//��Ʒ��
        JTextField c1=new JTextField(15);//����
        JTextField c2=new JTextField(15);//�ֿ���
        JTextField c3=new JTextField(15);//���ܺ�
        aa.addActionListener(new ActionListener(){
            public void actionPerformed(ActionEvent e)
            {
                try {
                    Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                    String url="******************************************************************************;";
                    String user="sa";//sa��������Ա
                    String password="123123";//����
                    Connection conn=DriverManager.getConnection(url,user,password);
                    Statement st=conn.createStatement();

                    String goods_name=c.getText().trim();
                    String goods_num=c1.getText().trim();
                    String stash_name=c2.getText().trim();
                    String shelf_id=c3.getText().trim();


                    String  s="(Select * from dbo.cargo where Sname='"+goods_name+"')";
                    ResultSet r=st.executeQuery(s);
                    if (goods_name.isEmpty()){
                        JOptionPane.showMessageDialog(null, "��������Ʒ��~","��ʾ��Ϣ",JOptionPane.WARNING_MESSAGE);
                    }else {
                        if(r.next())
                        {
                            JOptionPane.showMessageDialog(null, "����Ʒ�Ѵ���,�����ظ����~","��ʾ��Ϣ",JOptionPane.WARNING_MESSAGE);
                        }
                        else
                        {
                            if (stash_name.isEmpty()){
                                JOptionPane.showMessageDialog(null, "������ֿ���~","��ʾ��Ϣ",JOptionPane.WARNING_MESSAGE);
                            }else {
                                //��Ʒ��֤ͨ���󣬽�����Ҫ��֤���ܺ��Ƿ�����Ĳֿ���ƥ��
                                String  s4="select * from stash,shelf where Cname = '"+stash_name+"' and stash.Cid = shelf.Cid";
                                ResultSet r1 = st.executeQuery(s4);
                                //�����ѯ������ڣ�˵������Ĳֿ�����ȷ
                                if(r1.next()){
                                    String s5 = r1.getString(4).trim();
                                    //�������Ļ��ܺŵ�����ĸ��Ŀ��ֿ���ԭ�л��ܵ�����ĸ��ͬ
                                    if(shelf_id.startsWith(String.valueOf(s5.charAt(0)))){
                                        //���ж�����Ļ��ܺ��Ƿ��Ѿ��л��������������

                                        String  s6="select * from stash,shelf where stash.Cid = shelf.Cid";
                                        ResultSet r2 = st.executeQuery(s6);
                                        boolean r2_flag = r2.next();
                                        while (r2_flag){
                                            if(r2.getString(4).trim().equals(shelf_id)) {
                                                System.out.println(r2.getString(5));
                                                System.out.println(shelf_id);
                                                JOptionPane.showMessageDialog(null, "������Ļ��ܺ���ԭ�ֿ��Ѿ����ˣ��������ԣ�", "��ʾ��Ϣ", JOptionPane.WARNING_MESSAGE);
                                                r2_flag = true;
                                                break;
                                            }
                                            r2_flag = r2.next();
                                        }
                                        if (r2_flag == false){
                                            //���������еĻ��ܺź󣬻���Ҫ��������Ĳֿ�����ɸѡ���ֿ��ID
                                            String s7 = "select * from stash where Cname = '"+stash_name+"'";
                                            ResultSet r3 = st.executeQuery(s7);
                                            r3.next();
                                            //��ȡ�ֿ�ID
                                            String stash_id = r3.getString(1);
                                            String s8 = "insert into dbo.cargo(Sname,Snum) values('" + goods_name + "','" + goods_num + "')";
                                            int r4 = st.executeUpdate(s8);
                                            String s9 = "insert into dbo.shelf(Hid,Cid,Sname) values('"+shelf_id+"','"+stash_id+"','"+goods_name+"')";
                                            int r5 = st.executeUpdate(s9);
                                            JOptionPane.showMessageDialog(null, "��ӳɹ�!","�ɹ���Ϣ",JOptionPane.WARNING_MESSAGE);
                                        }
                                    }else {
                                        JOptionPane.showMessageDialog(null, "������Ļ��ܺ���ֿ�������!","��ʾ��Ϣ",JOptionPane.WARNING_MESSAGE);
                                    }
                                }else{
                                    JOptionPane.showMessageDialog(null, "������Ĳֿ��������������������","��ʾ��Ϣ",JOptionPane.WARNING_MESSAGE);
                                }
                            }
                        }
                    }
                    conn.close();
                }catch (ClassNotFoundException ex) {
                    System.out.println("û���ҵ���Ӧ�����ݿ�������");
                }
                catch (SQLException ex) {
                    System.out.println("���ݿ����ӻ��������ݿ����ʧ��");
                }
            }
        });

        //��������
        bb.addActionListener(new ActionListener(){
            public void actionPerformed(ActionEvent e)
            {
                c.setText("");
                c1.setText("");
                c2.setText("");
                c3.setText("");
            }
        });

        frame.setLayout(null);//���ɲ���
        j.setBounds(20, 30,  100, 20); c.setBounds(120, 30, 120, 25);
        j1.setBounds(20, 70, 100, 20);c1.setBounds(120, 70, 100, 25);
        j2.setBounds(20, 110, 100, 30);c2.setBounds(120, 110, 100, 25);
        j3.setBounds(20, 150, 100, 30);c3.setBounds(120, 150, 100, 25);

        aa.setBounds(100, 400, 100, 30); bb.setBounds(300, 400, 100, 30);
        j8.setBounds(10, 480, 450, 15);

        frame.add(j);
        frame.add(j1);
        frame.add(j2);
        frame.add(j3);
        frame.add(c);
        frame.add(c1);
        frame.add(c2);
        frame.add(c3);
        frame.add(aa);
        frame.add(bb);
        frame.add(j8);
    }
}