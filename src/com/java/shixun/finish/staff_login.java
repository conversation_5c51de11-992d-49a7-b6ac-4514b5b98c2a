package com.java.shixun.finish;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.*;
import javax.swing.*;

import com.java.shixun.VerificationCode;
import com.java.shixun.ZC;
import com.java.shixun.finish.staff_frame;


public class staff_login extends JFrame {
    private VerificationCode vcode = new VerificationCode();
    JTextField co;
    public static JTextField user;
    JPasswordField pass;
    JButton ok,register;

    public staff_login() {
        super("�����˵�¼");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        this.setSize(450, 350);
        this.setLocationRelativeTo(null);
        setVisible(true);
        Image img = Toolkit.getDefaultToolkit().getImage("pic\\finish\\login.png");
        //����ImagePanel����ӵ�JFrame
        ImagePanel frame = new ImagePanel(img);
        this.add(frame);
        //�˺������������ͼ��
        Icon login = new ImageIcon("pic\\finish\\user.jpg");
        JLabel I = new JLabel();
        I.setIcon(login);
        Icon password = new ImageIcon("pic\\finish\\password.png");
        JLabel p = new JLabel();
        p.setIcon(password);
        JLabel code = new JLabel("��֤��");
        code.setForeground(Color.WHITE);
        code.setFont(new Font("����", Font.BOLD, 17));

        user = new JTextField();
        pass = new JPasswordField();
        co = new JTextField();
        ok = new JButton("��¼");
        ok.setContentAreaFilled(false);
        register = new JButton("ע��");
        register.setContentAreaFilled(false);
        ok.setForeground(Color.WHITE);
        register.setForeground(Color.WHITE);
        I.setBounds(80, 50, 60, 40);
        p.setBounds(80, 100, 60, 40);
        code.setBounds(70, 150, 60, 40);
        user.setBounds(150, 50, 150, 30);
        pass.setBounds(150, 100, 150, 30);
        co.setBounds(150, 150, 150, 30);
        ok.setBounds(120, 220, 70, 30);
        register.setBounds(250, 220, 70, 30);
        vcode.setBounds(310, 145, 100, 40);
        frame.setLayout(null);
        frame.add(I);
        frame.add(p);
        frame.add(code);
        frame.add(user);
        frame.add(pass);
        frame.add(co);
        frame.add(ok);
        frame.add(register);
        frame.add(vcode);
        //����ע�ᰴťʱ
        register.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
            closeThis();
            JOptionPane.showMessageDialog(null,"��ת�ɹ�","��ʾ��Ϣ",JOptionPane.INFORMATION_MESSAGE);

            new ZC();
            }
        });

        //���¡���¼����ťʱ
        ok.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                String jusername = user.getText();
                char s[] = pass.getPassword();
                String jpassword = new String(s);
                String coo = co.getText();
                try {
                    String user="sa";//sa��������Ա
                    String password="123123";//����
                    Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                    //���ض�Ӧ��jdbc����
                    String url="******************************************************************************;";
                    //���������ַ���
                    Connection conn=DriverManager.getConnection(url,user,password);
                    //�������ݿ����Ӷ���
                    Statement st=conn.createStatement();
                    //����SQL���ִ�ж���
                    String  strSQL="(Select * from  dbo.staff where Pid='"+jusername+"'And Ppassword='"+jpassword+"' )";
                    ResultSet rs=st.executeQuery(strSQL);
                    if (coo.isEmpty()) {
                        JOptionPane.showMessageDialog(null, "��������֤��!", "��ʾ��Ϣ", JOptionPane.WARNING_MESSAGE);
                    } else if (!isValidCodeRight()) {
                        JOptionPane.showMessageDialog(null, "��֤���������������!", "��ʾ��Ϣ", JOptionPane.WARNING_MESSAGE);
                    } else if (rs.next()) {
                        JOptionPane.showMessageDialog(null, "��¼�ɹ�!", "��¼", JOptionPane.WARNING_MESSAGE);
                        new staff_frame();
                        closeThis();

                    } else {
                        JOptionPane.showMessageDialog(null, "�û��������ڻ���֤�����!", "����", JOptionPane.WARNING_MESSAGE);
                        conn.close();
                        //�ر����ݿ�����
                    }

                } catch (ClassNotFoundException ex) {
                    ex.printStackTrace();
                    JOptionPane.showMessageDialog(null, "û���ҵ���Ӧ�����ݿ�����", "��ʾ", JOptionPane.WARNING_MESSAGE);

                } catch (SQLException ex) {
                    ex.printStackTrace();
                    JOptionPane.showMessageDialog(null, "���ݿ����ӻ��������ݿ����ʧ��", "��ʾ", JOptionPane.WARNING_MESSAGE);
                }

            }

        });
    }
    public boolean isValidCodeRight() {
        if (co == null) {
            return false;
        } else if (vcode == null) {
            return true;
        } else if (vcode.getCode().equals(co.getText())) {
            return true;
        } else {
            return false;
        }
    }
    public void closeThis(){
        this.dispose();
        }

}