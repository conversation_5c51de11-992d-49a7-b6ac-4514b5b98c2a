package com.java.shixun.finish;

import com.java.shixun.finish.ImagePanel;
import com.java.shixun.finish.staff_login;

import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;
import java.sql.*;
import java.util.Vector;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;

public class staff_frame extends JFrame{
    JTabbedPane jtbp; //����ѡ�
    JPanel jp1,jp2,jp3;	//�������
    private JComboBox<String> candidatebox;

    public staff_frame()
    {
        super("�����˵�½");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        this.setSize(800, 600);
        this.setLocationRelativeTo(null);
        setVisible(true);
        MenuBar bar = new MenuBar();// �����˵���
        bar.setFont(new Font("����", Font.PLAIN, 30));
        Menu fileMenu = new Menu("FILE");// �������ļ����˵�
        fileMenu.setFont(new Font("����", Font.PLAIN, 17));
        MenuItem open = new MenuItem("OPEN");
        MenuItem exit = new MenuItem("EXIT");
        Menu help = new Menu("HELP");// ����������"�˵�
        help.setFont(new Font("����", Font.PLAIN, 17));
        MenuItem print = new MenuItem("PRINT");
        exit.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                System.out.println("�ɹ����봰����222��\n");
                new staff_login();
                System.out.println("�ɹ����봰����333��\n");
                System.out.println();
                closeThis();
            }
        });

        fileMenu.add(print);
        fileMenu.add(open);
        fileMenu.addSeparator();// ���ò˵��ָ���
        fileMenu.add(exit);
        bar.add(fileMenu);// ���ļ���ӵ��˵�����
        bar.add(help);// ���ļ���ӵ��˵�����
        setMenuBar(bar);// ���ò˵�����ʹ�����ַ�ʽ���ò˵������Բ�ռ�ò��ֿռ�

        //�������
        Image img = Toolkit.getDefaultToolkit().getImage("pic\\finish\\staff_frame.png");
        // ����ImagePanel����ӵ�JFrame
        jp1 = new ImagePanel(img);

        Image img1 = Toolkit.getDefaultToolkit().getImage("pic\\finish\\staff_frame.png");
        // ����ImagePanel����ӵ�JFrame
        jp2 = new ImagePanel(img1);

        Image img2 = Toolkit.getDefaultToolkit().getImage("pic\\finish\\staff_frame.png");
        // ����ImagePanel����ӵ�JFrame
        jp3 = new ImagePanel(img2);


        //jp1������ϵ�����
        String[][] datas = {};
        String[] titles = {"�����˹���","����������","�绰����","��¼����"};
        String[][] datas1 = {};
        String[] titles1 = {"���ܺ�","��Ʒ��","��Ʒ����"};

        DefaultTableModel myModel = new DefaultTableModel(datas, titles);
        DefaultTableModel myModel1 = new DefaultTableModel(datas1, titles1);
        JTable table = new JTable(myModel);// ������table��������Դ��myModel����
        JTable table1 = new JTable(myModel1);
        table.setPreferredScrollableViewportSize(new Dimension(550, 100));// ������ʾ�ߴ�
        table1.setPreferredScrollableViewportSize(new Dimension(550, 100));
        // ����һ���������������
        JScrollPane scrollPane = new JScrollPane(table);
        JScrollPane scrollPane1 = new JScrollPane(table1);
        //�и�
        table.setRowHeight(20);
        table1.setRowHeight(20);

        JLabel j1;
        JLabel j;
        JButton again;
        JButton again1;
        JScrollPane scrollPane2;
        try {
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            //���ض�Ӧ��jdbc����
            String url = "******************************************************************************;";
            //���������ַ���
            String user = "sa";//sa��������Ա
            String password = "123123";//����
            Connection conn = DriverManager.getConnection(url, user, password);
            //�������ݿ����Ӷ���
            Statement st = conn.createStatement();
            //����SQL���ִ�ж���

            //���˵�A ��һ�ű�
            String strSQL = "(Select * from  dbo.staff where Pid='" + staff_login.user.getText() + "')";
            ResultSet rs = st.executeQuery(strSQL);
            if (rs.next()) {
                Vector<String> ve = new Vector<String>();
                ve.addElement(rs.getString(1));
                ve.addElement(rs.getString(2));
                ve.addElement(rs.getString(3));
                ve.addElement(rs.getString(4));
                myModel.addRow(ve);
            }

            //���˵�A �ڶ��ű�
            String s1 = "(Select * from dbo.staff,dbo.stash,dbo.supervise where staff.Pid='" + staff_login.user.getText() + "' And staff.Pid=supervise.Pid And supervise.Cid = stash.Cid)";
            ResultSet r1 = st.executeQuery(s1);
            if (r1.next()) {
                String s2 = "(Select * from dbo.shelf,dbo.stash,dbo.cargo,dbo.supervise where supervise.Pid='" + r1.getString(8) + "' And stash.Cid=shelf.Cid AND shelf.Sname = cargo.Sname And stash.Cid = supervise.Cid)";
                //r1.getstring(7)������ǻ�ȡ��һ�β�ѯ������ĵ�7������
                ResultSet r2 = st.executeQuery(s2);
                while (r2.next()) {
                    Vector<String> ve1 = new Vector<String>();
                    ve1.addElement(r2.getString(1));
                    ve1.addElement(r2.getString(3));
                    ve1.addElement(r2.getString(8));
                    myModel1.addRow(ve1);
                }
            }

            again = new JButton("ˢ ��~");
            again.setForeground(Color.white);
            again.setContentAreaFilled(false);
            again.setFont(new Font("����", Font.BOLD, 14));
            //��� ��ˢ�¡� ��ť��
            again.addActionListener(new ActionListener() {
                public void actionPerformed(ActionEvent e) {
                    try {
                        Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                        String url = "******************************************************************************;";
                        String user = "sa";//sa��������Ա
                        String password = "123123";//����
                        Connection conn = DriverManager.getConnection(url, user, password);
                        Statement st = conn.createStatement();

                        while(myModel1.getRowCount()>0)
                        {
                            System.out.println("ȥ���˵�" + myModel1.getRowCount() + "��");
                            myModel1.removeRow(myModel1.getRowCount()-1);
                        }
                        //���˵�A �ڶ��ű� ˢ��
                        String s1 = "(Select * from dbo.staff,dbo.stash,dbo.supervise where staff.Pid='" + staff_login.user.getText() + "' And supervise.Cid = stash.Cid And supervise.Pid = staff.Pid)";
                        ResultSet r1 = st.executeQuery(s1);
                        if (r1.next()) {
                            String s2 = "(Select * from dbo.shelf,dbo.stash,dbo.cargo,dbo.supervise where supervise.Pid='" + r1.getString(8) + "' And stash.Cid=shelf.Cid AND shelf.Sname = cargo.Sname And supervise.Cid = stash.Cid)";
                            System.out.println(r1.getString(8));
                            ResultSet r2 = st.executeQuery(s2);
                            System.out.printf("����33\n");

                            while (r2.next()) {
                                System.out.printf("����55\n");
                                Vector<String> ve1 = new Vector<String>();
                                ve1.addElement(r2.getString(1));
                                ve1.addElement(r2.getString(3));
                                ve1.addElement(r2.getString(8));
                                myModel1.addRow(ve1);
                                System.out.printf("����66\n");
                            }
                            conn.close();
                        }
                    } catch (ClassNotFoundException ex) {
                        System.out.println("û���ҵ���Ӧ�����ݿ�������");
                    } catch (SQLException ex) {
                        System.out.println("���ݿ����ӻ��������ݿ����ʧ��22");
                    }
                }
            });

            //���˵�B
            String[][] datas2 = {};
            String[] titles2 = {"���ܺ�","��Ʒ��","��Ʒ����"};            //{"�γ̺�", "�γ���", "ѧ��"};
            DefaultTableModel myModel2 = new DefaultTableModel(datas2, titles2);
            JTable table2 = new JTable(myModel2);
            table2.setRowHeight(20);
            table2.setPreferredScrollableViewportSize(new Dimension(550, 400));
            scrollPane2 = new JScrollPane(table2);
            String s2 = "(Select * from dbo.shelf,dbo.stash,dbo.cargo,dbo.supervise where supervise.Pid='"+staff_login.user.getText()+"' And stash.Cid=shelf.Cid AND shelf.Sname = cargo.Sname And supervise.Cid = stash.Cid)";
            ResultSet r2 = st.executeQuery(s2);
            while (r2.next()) {
                Vector<String> ve2 = new Vector<String>();
                ve2.addElement(r2.getString(1));
                ve2.addElement(r2.getString(3));
                ve2.addElement(r2.getString(8));
                myModel2.addRow(ve2);
            }
            again1 = new JButton("ˢ ��~");
            again1.setForeground(Color.white);
            again1.setContentAreaFilled(false);
            again1.setFont(new Font("����", Font.BOLD, 14));
            again1.addActionListener(new ActionListener() {
                public void actionPerformed(ActionEvent e) {
                    try {
                        Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                        String url = "******************************************************************************;";
                        String user = "sa";//sa��������Ա
                        String password = "123123";//����
                        Connection conn = DriverManager.getConnection(url, user, password);
                        Statement st = conn.createStatement();

                        while(myModel2.getRowCount()>0)
                        {
                            myModel2.removeRow(myModel2.getRowCount()-1);
                        }

                        String s2 = "(Select * from dbo.shelf,dbo.stash,dbo.cargo,dbo.supervise where supervise.Pid='"+staff_login.user.getText()+"' And stash.Cid=shelf.Cid AND shelf.Sname = cargo.Sname And supervise.Cid = stash.Cid)";
                        ResultSet r2 = st.executeQuery(s2);

                        while (r2.next()) {
                            Vector<String> ve1 = new Vector<String>();
                            ve1.addElement(r2.getString(1));
                            ve1.addElement(r2.getString(3));
                            ve1.addElement(r2.getString(8));
                            myModel2.addRow(ve1);
                        }
                        conn.close();
                    } catch (ClassNotFoundException ex) {
                        System.out.println("û���ҵ���Ӧ�����ݿ�������");
                    } catch (SQLException ex) {
                        System.out.println("���ݿ����ӻ��������ݿ����ʧ��22");
                    }
                }
            });
            conn.close();
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(e);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

        JLabel goods_name = new JLabel("��������Ҫ�޸ĵ���Ʒ��:");
        goods_name.setForeground(Color.black);
        goods_name.setFont(new Font("����", Font.BOLD, 18));
        JLabel goods_num = new JLabel("��������Ҫ�޸ĵ�����(��:+50):");
        goods_num.setForeground(Color.black);
        goods_num.setFont(new Font("����", Font.BOLD, 18));

        // goods_name1���������û��������Ʒ��
        JTextField goods_name1 = new JTextField(20);
        // goods_num1��������������Ʒ������
        JTextField goods_num1 = new JTextField(20);

        JButton c = new JButton("ȷ��");
        //���ﻹ��Ҫ�ж��Ƿ��������ݣ���
        c.setFont(new Font("����", Font.BOLD, 20));
        c.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                try {
                    Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                    String url = "******************************************************************************;";
                    String user = "sa";//sa��������Ա
                    String password = "123123";//����
                    Connection conn = DriverManager.getConnection(url, user, password);
                    Statement st = conn.createStatement();

                    //��ȡ�û������ ����Ʒ����
                    String ok = goods_name1.getText().trim();
                    //�û������ ����Ʒ������
                    String ok_num = goods_num1.getText().trim();
                    String ok_num1 = ok_num;


                    if (ok.isEmpty() && ok_num1.isEmpty()){
                        JOptionPane.showMessageDialog(null, "��������Ϣ��","��ʾ��Ϣ",JOptionPane.WARNING_MESSAGE);
                    }else {
                        String s = "(Select * from dbo.shelf,dbo.stash,dbo.cargo,dbo.supervise where supervise.Pid='" + staff_login.user.getText() + "' And stash.Cid=shelf.Cid AND shelf.Sname = cargo.Sname AND supervise.Cid = stash.Cid AND shelf.Sname = '"+ok+"')";
                        ResultSet r = st.executeQuery(s);
                        if (r.next()) {
                            if (ok_num.isEmpty()){
                                JOptionPane.showMessageDialog(null, "������������","��ʾ��Ϣ",JOptionPane.WARNING_MESSAGE);
                            }
                            else {
                                ok_num = ok_num.substring(1);
                                int number = Integer.parseInt(ok_num);
                                if(ok_num1.startsWith("+")){
                                    System.out.printf("������ʾ����11\n");
                                    String addSql = "UPDATE dbo.cargo SET Snum = Snum + '"+number+"' WHERE cargo.Sname = '"+ok+"'";
                                    /*String addSql = "(UPDATE dbo.cargo SET Snum = Snum + '"+number+"' WHERE cargo.Sname = '"+ok+"')";*/   //������뱨������ԭ��Ϊ��()
                                    int i1 = st.executeUpdate(addSql);
                                    System.out.printf("������ʾ����22\n");
                                    if(i1 == 1){
                                        System.out.printf("������ʾ����33\n");

                                        JOptionPane.showMessageDialog(null, "��ӳɹ�","��ʾ��Ϣ",JOptionPane.WARNING_MESSAGE);
                                    }

                                }else if(ok_num1.startsWith("-")){
                                    System.out.printf("������ʾ����44\n");
                                    String deleteSql = "UPDATE dbo.cargo SET Snum = Snum - '"+number+"' WHERE cargo.Sname = '"+ok+"'";
                                    int i2 = st.executeUpdate(deleteSql);
                                    if(i2 == 1){
                                        JOptionPane.showMessageDialog(null, "ɾ���ɹ�","��ʾ��Ϣ",JOptionPane.WARNING_MESSAGE);
                                    }
                                }
                            }
                        }else
                        {
                            JOptionPane.showMessageDialog(null, "û����˵����Ʒ","��ʾ��Ϣ",JOptionPane.WARNING_MESSAGE);
                        }
                    }
                    conn.close();

                } catch (ClassNotFoundException ex) {
                    System.out.println("û���ҵ���Ӧ�����ݿ�������");
                } catch (SQLException ex) {
                    System.out.println("���ݿ����ӻ��������ݿ����ʧ��");
                }
            }
        });



        //���˵�C

        JLabel ja1 = new JLabel("�����ѯ����Ʒ����");
        ja1.setForeground(Color.white);
        JLabel ja2 = new JLabel("���Ĳֿ�����");
        ja2.setForeground(Color.white);
        JLabel ja3 = new JLabel("���Ļ��ܺţ�");
        ja3.setForeground(Color.white);

        ja1.setFont(new Font("����", Font.BOLD, 20));
        ja2.setFont(new Font("����", Font.BOLD, 20));
        ja3.setFont(new Font("����", Font.BOLD, 20));

        JTextField input_1 = new JTextField(15);
        candidatebox = new JComboBox<>();

        JTextField output_1 = new JTextField(15);
        JTextField output_2 = new JTextField(15);
        JButton c1 = new JButton("�� ѯ");

        input_1.addKeyListener(new KeyListener() {
            @Override
            public  void  keyTyped(KeyEvent  e)  {
                String  query  =  input_1.getText().trim();
                //�������ԭ����ѡ���������
                candidatebox.removeAllItems();
                if  (query.isEmpty())  {
                    candidatebox.setVisible(false);
                }  else  {
                    try  {
                        candidatebox.setVisible(true);
                        Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                        String  url  =  "******************************************************************************;";
                        String  user  =  "sa";  //  sa��������Ա
                        String  password  =  "123123";  //  ����
                        Connection  conn  =  DriverManager.getConnection(url,  user,  password);
                        Statement  st  =  conn.createStatement();

                        String  L  =  "(select  *  from  shelf,stash,cargo,supervise  where  supervise.Pid  =  '"  +  staff_login.user.getText()  +  "'  and  supervise.Cid  =  stash.Cid  and  stash.Cid  =  shelf.Cid  and  shelf.Sname  =  cargo.Sname  and  cargo.Sname  LIKE  '%"  +  query  +  "%')";
                        ResultSet  rs  =  st.executeQuery(L);

                        while  (rs.next())  {
                            candidatebox.addItem(rs.getString(7));
                        }

                    }  catch  (SQLException  ex)  {
                        ex.printStackTrace();
                    }  catch  (ClassNotFoundException  ex)  {
                        throw  new  RuntimeException(ex);
                    }
                }
            }

//  ����������ʵ�ֿ��Ա��ֲ���


            @Override
            public void keyPressed(KeyEvent e) {

            }

            @Override
            public void keyReleased(KeyEvent e) {

            }
        });

        candidatebox.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (candidatebox.getItemCount() != 0){
                    String selectedValue = candidatebox.getSelectedItem().toString();
                    input_1.setText(selectedValue);
                }else {
                    System.out.println("�Ѿ�û����������");
                }
            }
        });



        c1.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                try {
                    Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                    String url = "******************************************************************************;";
                    String user = "sa";//sa��������Ա
                    String password = "123123";//����
                    Connection conn = DriverManager.getConnection(url, user, password);
                    Statement st = conn.createStatement();

                    String B1 = input_1.getText().trim();


                    String L = "(select * from shelf,stash,cargo,supervise where supervise.Pid = '"+staff_login.user.getText()+"' and supervise.Cid = stash.Cid and stash.Cid = shelf.Cid and shelf.Sname = cargo.Sname and cargo.Sname = '"+B1+"' )";
                    ResultSet M = st.executeQuery(L);
                    if (M.next()) {
                        System.out.printf("����111\n");
                        output_1.setText(M.getString(5));
                        output_2.setText(M.getString(1));
                    } else {
                        JOptionPane.showMessageDialog(null, "û�и���Ʒ������ϵ�ܹ����", "��ʾ��Ϣ", JOptionPane.WARNING_MESSAGE);
                    }
                    conn.close();

                } catch (ClassNotFoundException ex) {
                    System.out.println("û���ҵ���Ӧ�����ݿ�������");
                } catch (SQLException ex) {
                    System.out.println("���ݿ����ӻ��������ݿ����ʧ��");
                }
            }
        });

        jp1.setLayout(null);//���ɲ���
        jp2.setLayout(null);//���ɲ���
        jp3.setLayout(null);//���ɲ���

        //jp1�������λ��
        scrollPane.setBounds(50, 190, 550, 70);
        scrollPane1.setBounds(50, 290, 550, 100);
        again.setBounds(490, 140, 80, 30);


        //jp2�������λ��
        scrollPane2.setBounds(50, 20, 550, 250);

        goods_name.setBounds(20, 300, 250, 30);
        goods_name1.setBounds(50, 350, 150, 25);

        goods_num.setBounds(270, 300, 300, 30);
        goods_num1.setBounds(300, 350, 300, 25);

        again1.setBounds(100, 470, 80, 30);
        c.setBounds(500, 470, 80, 27);


        //jp3�������λ��
        ja1.setBounds(50, 50, 200, 30);
        ja2.setBounds(80, 220, 150, 30);
        ja3.setBounds(80, 270, 150, 30);

        input_1.setBounds(260, 50, 150, 25);
        output_1.setBounds(260, 220, 100, 25);
        output_2.setBounds(260, 270, 100, 25);
        c1.setBounds(450, 50, 70, 30);
        candidatebox.setBounds(260,80,150,25);




        // ����������jp1������
        jp1.add(scrollPane);
        jp1.add(scrollPane1);
        jp1.add(again);

        // ����������jp2������
        jp2.add(scrollPane2);
        jp2.add(goods_name);
        jp2.add(goods_name1);
        jp2.add(goods_num);
        jp2.add(goods_num1);
        jp2.add(again1);
        jp2.add(c);

        // ����������jp3������
        jp3.add(ja1);
        jp3.add(ja2);
        jp3.add(ja3);

        jp3.add(input_1);
        jp3.add(output_1);
        jp3.add(output_2);
        jp3.add(c1);
        jp3.add(candidatebox);

        jtbp = new JTabbedPane(JTabbedPane.LEFT); //����ѡ���ʹѡ���ֱ����
        jtbp.add("������Ϣ", jp1);
        jtbp.add("��������", jp2);
        jtbp.add("��Ʒ��ѯ", jp3);
        jtbp.setFont(new Font("����", Font.PLAIN, 30));
        this.add(jtbp);
    }

    public  void closeThis()//�رյ�ǰ����
    {
        this.dispose();
    }
}