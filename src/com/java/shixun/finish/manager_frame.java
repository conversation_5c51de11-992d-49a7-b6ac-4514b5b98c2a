package com.java.shixun.finish;

import  com.java.shixun.TransparentRadioButton;
import com.java.shixun.update;
import com.java.shixun.delete;

import com.java.shixun.finish.add;
import com.java.shixun.finish.ImagePanel;
import com.java.shixun.finish.manager_login;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Arrays;
import java.util.Vector;
import java.util.stream.Collectors;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;

public class manager_frame extends J<PERSON>rame {
    /**
     *
     */
    private JComboBox<String> candidatebox;//���д��봴����һ���µ�JComboBox���󲢽�������candidatebox������֮�����������������б�������ַ���Ԫ�أ����ߴ����û�ѡ��ĳ��ѡ��ʱ�Ķ�����
    public manager_frame() {
        super("�ܹܵ�¼");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        this.setSize(800, 800);
        this.setLocationRelativeTo(null);
        setVisible(true);


        Image img = Toolkit.getDefaultToolkit().getImage("pic\\finish\\manager_frame.png");
        // ����ImagePanel����ӵ�JFrame
        ImagePanel frame = new ImagePanel(img);
        this.add(frame);

        //��ѡ�򼯺�
        //���²�ѯ����Ҫ�õ�ģ����ѯ��

        ButtonGroup group = new ButtonGroup();
        JRadioButton option1 = new TransparentRadioButton("�����˲�ѯ");
        option1.setBackground(new Color(0, 0, 0, 0)); // ���ñ���Ϊ͸��
        option1.setForeground(Color.white);

        JRadioButton option2 = new TransparentRadioButton("��Ʒ����ѯ");
        option2.setBackground(new Color(0, 0, 0, 0)); // ���ñ���Ϊ͸��
        option2.setForeground(Color.white);

        JRadioButton option3 = new TransparentRadioButton("�ֿ��ѯ");
        option3.setBackground(new Color(0, 0, 0, 0)); // ���ñ���Ϊ͸��
        option3.setForeground(Color.white);

        JRadioButton option4 = new TransparentRadioButton("���ܲ�ѯ");
        option4.setBackground(new Color(0, 0, 0, 0)); // ���ñ���Ϊ͸��
        option4.setForeground(Color.white);

        group.add(option1);
        group.add(option2);
        group.add(option3);
        group.add(option4);



        //����A�����еĸ������
        JButton component1 = new JButton("���");
        component1.setBackground(Color.blue);
        JButton component2 = new JButton("�޸�");
        component2.setBackground(Color.green);
        JButton component3 = new JButton("ɾ��");
        component3.setBackground(Color.orange);
        JButton component4 = new JButton("�˳�");//��ɾ���˰�ť
        component4.setBackground(Color.magenta);

        JLabel component5 = new JLabel("����������:");
        component5.setFont(new Font("����", Font.PLAIN, 17));//������������壬���ӣ���С
        component5.setForeground(Color.white);
        JTextField component6 = new JTextField(20);//����������
        candidatebox = new JComboBox<>();



        JButton component7 = new JButton("��ѯ");//��ѯ��ť
        component7.setBackground(Color.cyan);
        JButton component8 = new JButton("ȫ �� �� Ϣ չ ʾ");
        component8.setBackground(Color.CYAN);
        component8.setFont(new Font("����", Font.BOLD, 17));
        JButton component9 = new JButton("<html><body>һ����ѯ����<br/>δ����ֿ�</body></html>");
        component9.setBackground(Color.CYAN);
        component9.setFont(new Font("����", Font.BOLD, 17));

        frame.setLayout(null);//���ɲ���
        component1.setBounds(20, 20, 70, 30);
        component2.setBounds(105, 20, 70, 30);
        component3.setBounds(20, 90, 70, 30);
        component4.setBounds(105, 90, 70, 30);

        component5.setBounds(370, 30, 500, 25);
        component6.setBounds(500, 30, 200, 25);
        candidatebox.setBounds(500,60,200,25);

        component7.setBounds(715, 17, 60, 30);
        option1.setBounds(495,130,100,20);
        option2.setBounds(495,165,100,20);
        option3.setBounds(615,130,100,20);
        option4.setBounds(615,165,100,20);
        component8.setBounds(460, 195, 210, 30);
        component9.setBounds(0,400,150,50);


        //������������봰�������ȥ
        frame.add(component1);
        frame.add(component2);
        frame.add(component3);
        frame.add(component4);
        frame.add(component5);
        frame.add(component6);
        frame.add(component7);
        frame.add(component8);
        frame.add(component9);
        frame.add(option1);
        frame.add(option2);
        frame.add(option3);
        frame.add(option4);
        frame.add(candidatebox);

        //�ĸ����
        String[][] datas = {};
        String[] titles = {"�����˹���", "����", "�绰", "����"};        //"�����˹���", "����", "�绰", "����"
        String[][] datas1 = {};
        String[] titles1 = {"�ֿ��", "�ֿ���", "λ��"};  //"�ֿ��", "�ֿ���", "λ��"
        String[][] datas2 = {};
        String[] titles2 = {"���ܺ�", "��Ʒ��"};                    //"���ܺ�", "��Ʒ��"
        String[][] datas3 = {};
        String[] titles3 = {"��Ʒ��", "����"};                        //"��Ʒ��", "����"

        DefaultTableModel myModel = new DefaultTableModel(datas, titles);// myModel��ű�������
        DefaultTableModel myModel1 = new DefaultTableModel(datas1, titles1);
        DefaultTableModel myModel2 = new DefaultTableModel(datas2, titles2);
        DefaultTableModel myModel3 = new DefaultTableModel(datas3, titles3);

        JTable table = new JTable(myModel);// ������table��������Դ��myModel����
        JTable table1 = new JTable(myModel1);
        JTable table2 = new JTable(myModel2);
        JTable table3 = new JTable(myModel3);

        table.setPreferredScrollableViewportSize(new Dimension(600, 100));// ������ʾ�ߴ�
        table1.setPreferredScrollableViewportSize(new Dimension(600, 100));
        table2.setPreferredScrollableViewportSize(new Dimension(600, 100));
        table3.setPreferredScrollableViewportSize(new Dimension(600, 100));

        // ����һ���������������
        JScrollPane scrollPane = new JScrollPane(table);
        JScrollPane scrollPane1 = new JScrollPane(table1);
        JScrollPane scrollPane2 = new JScrollPane(table2);
        JScrollPane scrollPane3 = new JScrollPane(table3);

        //����λ��
        scrollPane3.setBounds(170, 230, 600, 120);
        scrollPane.setBounds(170, 360, 600, 120);
        scrollPane1.setBounds(170, 490, 600, 120);
        scrollPane2.setBounds(170, 620, 600, 120);

        // ��������������������B������
        frame.setLayout(null);//���ɲ���
        frame.add(scrollPane);
        frame.add(scrollPane1);
        frame.add(scrollPane2);
        frame.add(scrollPane3);
        //��
        component1.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                new add();
            }
        });
        //��
        component2.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                new update();
            }
        });
        //ɾ
        component3.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                new delete();
            }
        });
        //�˳���¼
        component4.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                new manager_login();
                closeThis();
            }
        });


        component6.addKeyListener(new KeyListener() {
            @Override
            public void keyTyped(KeyEvent e) {
                String  query  =  component6.getText().trim();
                //�������ԭ����ѡ���������
                candidatebox.removeAllItems();
                if  (query.isEmpty())  {
                    candidatebox.setVisible(false);
                }else  {
                    try  {
                        candidatebox.setVisible(true);
                        Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                        String  url  =  "******************************************************************************;";
                        String  user  =  "sa";  //  sa��������Ա
                        String  password  =  "123123";  //  ����
                        Connection  conn  =  DriverManager.getConnection(url,  user,  password);
                        Statement  st  =  conn.createStatement();

                        if (option1.isSelected()) {
                            String L = "select * from staff where staff.Pname like '%"+query+"%'";
                            ResultSet rs = st.executeQuery(L);
                            boolean flag = rs.next();

                            if (flag == true){
                                while (flag) {
                                    candidatebox.addItem(rs.getString(2));
                                    flag = rs.next();
                                }
                                rs.close(); // ������رս����
                            }
                            else {
                                String L1 = "select * from staff where staff.Pid like '%"+query+"%'";
                                ResultSet rs1 = st.executeQuery(L1); // �����ﴴ���µĽ��������
                                boolean flag1 = rs1.next();

                                while (flag1) {
                                    candidatebox.addItem(rs1.getString(1));
                                    flag1 = rs1.next();
                                }
                                rs1.close(); // ������رս����
                            }

                        }
                        else if (option2.isSelected()) {
                            String L = "select * from cargo where Sname like '%"+query+"%'";
                            ResultSet rs = st.executeQuery(L);
                            while (rs.next()) {
                                candidatebox.addItem(rs.getString(1));
                            }
                        }
                        else if (option3.isSelected()) {
                            String L = " select * from stash where (Cid like '%"+query+"%' or Cname like '%"+query+"%')";
                            ResultSet rs = st.executeQuery(L);
                            while (rs.next()) {
                                candidatebox.addItem(rs.getString(1));
                            }
                        }
                        else if (option4.isSelected()) {
                            String L = "select * from shelf where shelf.Hid like '%"+query+"%'";
                            ResultSet rs = st.executeQuery(L);
                            while (rs.next()) {
                                candidatebox.addItem(rs.getString(1));
                            }
                        }

                    }  catch  (SQLException  ex)  {
                        ex.printStackTrace();
                    }  catch  (ClassNotFoundException  ex)  {
                        throw  new  RuntimeException(ex);
                    }
                }
            }

            @Override
            public void keyPressed(KeyEvent e) {

            }

            @Override
            public void keyReleased(KeyEvent e) {

            }
        });
        candidatebox.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (candidatebox.getItemCount() != 0){
                    String selectedValue = candidatebox.getSelectedItem().toString();
                    component6.setText(selectedValue);
                }else {
                    System.out.println("�Ѿ�û����������123");
                }
            }
        });

        //�����¡���ѯ����ť
        component7.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                try {
                    Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                    //���ض�Ӧ��jdbc����
                    String url = "******************************************************************************;";
                    //���������ַ���
                    String user = "sa";//sa��������Ա
                    String password = "123123";//����
                    Connection conn = DriverManager.getConnection(url, user, password);
                    //�������ݿ����Ӷ���
                    Statement st = conn.createStatement();
                    //����SQL���ִ�ж���

                    while (myModel.getRowCount() > 0) {
                        myModel.removeRow(myModel.getRowCount() - 1);
                    }
                    while (myModel1.getRowCount() > 0) {
                        myModel1.removeRow(myModel1.getRowCount() - 1);
                    }
                    while (myModel2.getRowCount() > 0) {
                        myModel2.removeRow(myModel2.getRowCount() - 1);
                    }
                    while (myModel3.getRowCount() > 0) {
                        myModel3.removeRow(myModel3.getRowCount() - 1);
                    }//��ձ��ϵĶ���

                    String ID = component6.getText().trim();

                    if (ID.isEmpty()) {
                        JOptionPane.showMessageDialog(null, "����������~", "��ʾ��Ϣ", JOptionPane.WARNING_MESSAGE);
                    }
                    else if (option1.isSelected()) {
                        //��ѡ�񡰸����˲�ѯ��ʱ��
                        String s = "(Select * from dbo.staff, dbo.supervise, dbo.stash where (staff.Pid='" + ID + "' OR staff.Pname='" + ID + "') And staff.Pid = supervise.Pid And supervise.Cid = stash.Cid)";
                        ResultSet r = st.executeQuery(s);
                        boolean flag1 = true;
                        boolean determine = r.next();
                        if (determine == false) {
                            JOptionPane.showMessageDialog(null, "�������/δ��ѯ����������Ϣ����������룡", "������Ϣ", JOptionPane.ERROR_MESSAGE);
                        }
                        else {
                            while (determine) {
                                if (flag1) {
                                    Vector<String> ve = new Vector<String>();
                                    ve.addElement(r.getString(1));
                                    ve.addElement(r.getString(2));
                                    ve.addElement(r.getString(3));
                                    ve.addElement(r.getString(4));
                                    myModel.addRow(ve);
                                    flag1 = false;
                                }
                                Vector<String> ve1 = new Vector<String>();
                                ve1.addElement(r.getString(7));
                                ve1.addElement(r.getString(8));
                                ve1.addElement(r.getString(9));
                                myModel1.addRow(ve1);
                                determine = r.next();
                            }

                            String strSQL = "select * from staff where Pid = '" + ID + "' or Pname = '" + ID + "'";
                            ResultSet rs = st.executeQuery(strSQL);
                            rs.next();
                            String ID1 = rs.getString(1);
                            String createViewSql = "select Hid,cargo.Sname,Snum from cargo join(select Hid,shelf.Sname,stash.Cid from stash,shelf,supervise where supervise.Pid = '" + ID1 + "' and stash.Cid = shelf.Cid and supervise.Cid = stash.Cid)temp1 on temp1.Sname = cargo.Sname";
                            ResultSet r1 = st.executeQuery(createViewSql);
                            while (r1.next()) {
                                Vector<String> ve1 = new Vector<String>();
                                ve1.addElement(r1.getString(1));
                                ve1.addElement(r1.getString(2));
                                myModel2.addRow(ve1);

                                Vector<String> ve2 = new Vector<String>();
                                ve2.addElement(r1.getString(2));
                                ve2.addElement(r1.getString(3));
                                myModel3.addRow(ve2);
                            }
                        }
                    }
                    else if (option2.isSelected()) {
                        String strSQL = "select * from cargo,shelf,staff,supervise,stash where cargo.Sname = shelf.Sname and shelf.Cid = stash.Cid and stash.Cid = supervise.Cid and supervise.Pid = staff.Pid and cargo.Sname = '" + ID + "'";
                        ResultSet rs = st.executeQuery(strSQL);

                        boolean determine = rs.next();
                        if (determine == false) {
                            JOptionPane.showMessageDialog(null, "�������/δ��ѯ����Ʒ��Ϣ�����飡", "������Ϣ", JOptionPane.ERROR_MESSAGE);
                        }
                        else {
                            while (determine) {

                                Vector<String> ve = new Vector<String>();
                                ve.addElement(rs.getString(6));
                                ve.addElement(rs.getString(7));
                                ve.addElement(rs.getString(8));
                                ve.addElement(rs.getString(9));
                                myModel.addRow(ve);

                                Vector<String> ve1 = new Vector<String>();
                                ve1.addElement(rs.getString(12));
                                ve1.addElement(rs.getString(13));
                                ve1.addElement(rs.getString(14));
                                myModel1.addRow(ve1);

                                Vector<String> ve2 = new Vector<String>();
                                ve2.addElement(rs.getString(3));
                                ve2.addElement(rs.getString(5));
                                myModel2.addRow(ve2);

                                Vector<String> ve3 = new Vector<String>();
                                ve3.addElement(rs.getString(1));
                                ve3.addElement(rs.getString(2));
                                myModel3.addRow(ve3);

                                determine = rs.next();
                            }
                        }

                    }
                    else if (option3.isSelected()) {
                        String s = "select * from cargo,shelf,staff,supervise,stash where cargo.Sname = shelf.Sname and shelf.Cid = stash.Cid and stash.Cid = supervise.Cid and supervise.Pid = staff.Pid and (stash.Cid = '" + ID + "' or stash.Cname = '" + ID + "')";
                        ResultSet r = st.executeQuery(s);
                        boolean flag1 = true;

                        boolean determine = r.next();
                        if (determine == false) {

                            //�ֿ��п�����û�˹����
                            String s1 = "select * from cargo,shelf,stash where cargo.Sname = shelf.Sname and shelf.Cid = stash.Cid and stash.Cid = '"+ID+"'";
                            ResultSet r1 = st.executeQuery(s1);
                            boolean flag11 = true;

                            boolean determine1 = r1.next();
                            if (determine1) {
                                while (determine1){
                                    if (flag11){
                                        Vector<String> ve1 = new Vector<String>();
                                        ve1.addElement(r1.getString(6));
                                        ve1.addElement(r1.getString(7));
                                        ve1.addElement(r1.getString(8));
                                        myModel1.addRow(ve1);
                                        flag11 = false;
                                    }
                                    Vector<String> ve2 = new Vector<String>();
                                    ve2.addElement(r1.getString(3));
                                    ve2.addElement(r1.getString(5));
                                    myModel2.addRow(ve2);

                                    Vector<String> ve3 = new Vector<String>();
                                    ve3.addElement(r1.getString(1));
                                    ve3.addElement(r1.getString(2));
                                    myModel3.addRow(ve3);
                                    determine1 = r1.next();

                                }
                            }
                            else {
                                JOptionPane.showMessageDialog(null, "�������/δ��ѯ���ֿ���Ϣ����������룡", "������Ϣ", JOptionPane.ERROR_MESSAGE);
                            }

                        }
                        else {
                            while (determine) {
                                if (flag1) {
                                    Vector<String> ve = new Vector<String>();
                                    ve.addElement(r.getString(6));
                                    ve.addElement(r.getString(7));
                                    ve.addElement(r.getString(8));
                                    ve.addElement(r.getString(9));
                                    myModel.addRow(ve);
                                    Vector<String> ve1 = new Vector<String>();
                                    ve1.addElement(r.getString(12));
                                    ve1.addElement(r.getString(13));
                                    ve1.addElement(r.getString(14));
                                    myModel1.addRow(ve1);
                                    flag1 = false;
                                }

                                Vector<String> ve2 = new Vector<String>();
                                ve2.addElement(r.getString(3));
                                ve2.addElement(r.getString(5));
                                myModel2.addRow(ve2);

                                Vector<String> ve3 = new Vector<String>();
                                ve3.addElement(r.getString(1));
                                ve3.addElement(r.getString(2));
                                myModel3.addRow(ve3);

                                determine = r.next();
                            }
                        }


                    }
                    else if (option4.isSelected()) {
                        String strSQL = "select * from cargo,shelf,staff,supervise,stash where cargo.Sname = shelf.Sname and shelf.Cid = stash.Cid and stash.Cid = supervise.Cid and supervise.Pid = staff.Pid and shelf.Hid = '" + ID + "'";
                        ResultSet rs = st.executeQuery(strSQL);

                        boolean determine = rs.next();
                        if (determine == false) {
                            JOptionPane.showMessageDialog(null, "δ��ѯ�����ܣ�", "������Ϣ", JOptionPane.ERROR_MESSAGE);
                        }
                        else {
                            while (determine) {

                                Vector<String> ve = new Vector<String>();
                                ve.addElement(rs.getString(6));
                                ve.addElement(rs.getString(7));
                                ve.addElement(rs.getString(8));
                                ve.addElement(rs.getString(9));
                                myModel.addRow(ve);

                                Vector<String> ve1 = new Vector<String>();
                                ve1.addElement(rs.getString(12));
                                ve1.addElement(rs.getString(13));
                                ve1.addElement(rs.getString(14));
                                myModel1.addRow(ve1);

                                Vector<String> ve2 = new Vector<String>();
                                ve2.addElement(rs.getString(3));
                                ve2.addElement(rs.getString(5));
                                myModel2.addRow(ve2);

                                Vector<String> ve3 = new Vector<String>();
                                ve3.addElement(rs.getString(1));
                                ve3.addElement(rs.getString(2));
                                myModel3.addRow(ve3);

                                determine = rs.next();
                            }
                        }
                    }

                    conn.close();

                } catch (ClassNotFoundException ex) {
                    System.out.println("û���ҵ���Ӧ�����ݿ�������");
                } catch (SQLException ex) {
                    System.out.println("���ݿ����ӻ��������ݿ����ʧ��");
                }

            }
        });

        //������ ����ѯ������Ϣʱ��
        component8.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                try {
                    Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                    //���ض�Ӧ��jdbc����
                    String url = "******************************************************************************;";
                    //���������ַ���
                    String user = "sa";//sa��������Ա
                    String password = "123123";//����
                    Connection conn = DriverManager.getConnection(url, user, password);
                    //�������ݿ����Ӷ���
                    Statement st = conn.createStatement();
                    //����SQL���ִ�ж���

                    while (myModel.getRowCount() > 0) {
                        myModel.removeRow(myModel.getRowCount() - 1);
                    }

                    while (myModel1.getRowCount() > 0) {
                        myModel1.removeRow(myModel1.getRowCount() - 1);
                    }

                    while (myModel2.getRowCount() > 0) {
                        myModel2.removeRow(myModel2.getRowCount() - 1);
                    }
                    while (myModel3.getRowCount() > 0) {
                        myModel3.removeRow(myModel3.getRowCount() - 1);
                    }


                    String strSQL = "(Select * from  dbo.staff)";
                    ResultSet rs = st.executeQuery(strSQL);
                    while (rs.next()) {
                        Vector<String> v = new Vector<String>();
                        v.addElement(rs.getString(1));
                        v.addElement(rs.getString(2));
                        v.addElement(rs.getString(3));
                        v.addElement(rs.getString(4));
                        myModel.addRow(v);
                    }
                    String strSQL1 = "(Select * from  dbo.stash)";
                    ResultSet rs1 = st.executeQuery(strSQL1);
                    while (rs1.next()) {
                        Vector<String> v1 = new Vector<String>();
                        v1.addElement(rs1.getString(1));
                        v1.addElement(rs1.getString(2));
                        v1.addElement(rs1.getString(3));
                        myModel1.addRow(v1);
                    }
                    String strSQL2 = "(Select * from  dbo.shelf)";
                    ResultSet rs2 = st.executeQuery(strSQL2);
                    while (rs2.next()) {
                        Vector<String> v2 = new Vector<String>();
                        v2.addElement(rs2.getString(1));
                        v2.addElement(rs2.getString(3));
                        myModel2.addRow(v2);
                    }
                    String strSQL3 = "(Select * from  dbo.cargo)";
                    ResultSet rs3 = st.executeQuery(strSQL3);
                    while (rs3.next()) {
                        Vector<String> v3 = new Vector<String>();
                        v3.addElement(rs3.getString(1));
                        v3.addElement(rs3.getString(2));
                        myModel3.addRow(v3);
                    }
                    conn.close();
                } catch (ClassNotFoundException ex) {
                    System.out.println("û���ҵ���Ӧ�����ݿ�������");
                } catch (SQLException ex) {
                    System.out.println("���ݿ����ӻ��������ݿ����ʧ��");
                }
            }

        });

        component9.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                try{
                    Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                    //���ض�Ӧ��jdbc����
                    String url = "******************************************************************************;";
                    //���������ַ���
                    String user = "sa";//sa��������Ա
                    String password = "123123";//����
                    Connection conn = DriverManager.getConnection(url, user, password);
                    //�������ݿ����Ӷ���
                    Statement st = conn.createStatement();
                    //����SQL���ִ�ж���

                    String result = "";
                    String result1 = "";
                    StringBuilder sb = new StringBuilder();


                    while (myModel.getRowCount() > 0) {
                        myModel.removeRow(myModel.getRowCount() - 1);
                    }

                    while (myModel1.getRowCount() > 0) {
                        myModel1.removeRow(myModel1.getRowCount() - 1);
                    }

                    while (myModel2.getRowCount() > 0) {
                        myModel2.removeRow(myModel2.getRowCount() - 1);
                    }
                    while (myModel3.getRowCount() > 0) {
                        myModel3.removeRow(myModel3.getRowCount() - 1);
                    }

                    String s = "select * from cargo,shelf,staff,supervise,stash where cargo.Sname = shelf.Sname and shelf.Cid = stash.Cid and stash.Cid = supervise.Cid and supervise.Pid = staff.Pid order by stash.Cid";
                    ResultSet r = st.executeQuery(s);
                    //���Բ�ѯ���Ѿ��󶨸����˵Ĳֿ⣬��ô���ǾͿ���ͨ���ų��������û�а󶨸����˵Ĳֿ⡣
                    while (r.next()) {
                        result += r.getString(4).trim() + " ";
                    }
                    String collect = Arrays.stream(result.split(" ")).distinct().collect(Collectors.joining(" "));
                    System.out.println(collect);
                    String[] str = collect.split(" ");


                    String s1 = "select * from cargo,shelf,stash where cargo.Sname = shelf.Sname and shelf.Cid = stash.Cid order by stash.Cid";
                    ResultSet r1 = st.executeQuery(s1);
                    while (r1.next()){
                        result1 += r1.getString(4).trim() + " ";
                    }
                    String collect1 = Arrays.stream(result1.split(" ")).distinct().collect(Collectors.joining(" "));
                    System.out.println(collect1);
                    String[] str1 = collect1.split(" ");

                    for (int i = 0;i<str.length;i++){
                        String s_ = str[i];
                        if (!collect1.contains(s_)){
                            sb.append(s_ + "");
                        }
                    }

                    for (int i = 0;i<str1.length;i++){
                        String s_1 = str1[i];
                        if (!collect.contains(s_1)){
                            sb.append(s_1 + "");
                        }
                    }
                    String result2 = sb.substring(0, sb.length());
                    System.out.println(result2);
                    String[] str2 = result2.split("");

                    for (int i = 0;i<str2.length;i++){
                        System.out.println(str2.length);
                        if (result2.isEmpty()){
                            break;
                        }
                        String temp = str2[i]+str2[i+1]+str2[i+2];
                        i = i+2;
                        String query = "select * from cargo,shelf,stash where cargo.Sname = shelf.Sname and shelf.Cid = stash.Cid and stash.Cid = '"+temp+"'";
                        ResultSet rs = st.executeQuery(query);

                        boolean flag1 = true;
                        while (rs.next()){
                            if (flag1){
                                Vector<String> v = new Vector<String>();
                                v.addElement(rs.getString(6));
                                v.addElement(rs.getString(7));
                                v.addElement(rs.getString(8));
                                myModel1.addRow(v);
                                flag1 = false;
                            }

                            Vector<String> v2 = new Vector<String>();
                            v2.addElement(rs.getString(3));
                            v2.addElement(rs.getString(5));
                            myModel2.addRow(v2);

                            Vector<String> v3 = new Vector<String>();
                            v3.addElement(rs.getString(1));
                            v3.addElement(rs.getString(2));
                            myModel3.addRow(v3);
                        }
                    }

                    conn.close();
                }catch (ClassNotFoundException ex) {
                    System.out.println("û���ҵ���Ӧ�����ݿ�������");
                } catch (SQLException ex) {
                    System.out.println("���ݿ����ӻ��������ݿ����ʧ��");
                }
            }
        });
    }

    public void closeThis()//�رյ�ǰ����
    {
        this.dispose();
    }

}
