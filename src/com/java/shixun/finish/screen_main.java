package com.java.shixun.finish;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import javax.swing.JButton;
import javax.swing.JFrame;

public class screen_main extends J<PERSON>rame {
        public screen_main() {
        setTitle("��¼");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        this.setSize(800, 470);
        this.setLocation(300,120);
        Image img = Toolkit.getDefaultToolkit().getImage("pic\\finish\\open.png");
        ImagePanel B = new ImagePanel(img);
        this.add(B);
        JButton manager = new JButton("�ܹܵ�¼");
        manager.setContentAreaFilled(false);
        manager.setForeground(Color.WHITE);
        JButton staff = new JButton("�����˵�¼");
        staff.setContentAreaFilled(false);
        staff.setForeground(Color.WHITE);
        manager.setBounds(200,300,100,30);
        staff.setBounds(450,300,130,30);
        manager.setFont(new Font("΢���ź�",Font.BOLD,15));
        B.setLayout(null);
        B.add(manager);
        B.add(staff);

        staff.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e){
                new staff_login();
            }
        });
        manager.addActionListener(new ActionListener() {
           public void actionPerformed(ActionEvent e){
               new manager_login();
           }
        });
        }
        public static void main(String[] args){
            new screen_main().setVisible(true);
            }

}
