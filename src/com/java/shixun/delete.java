package com.java.shixun;

import com.java.shixun.finish.ImagePanel;

import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

import javax.swing.*;

public class delete extends JFrame{

    /**
     *
     */
    //private static final long serialVersionUID = 1L;

    public delete() {
        super("ɾ����Ϣ");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        this.setSize(500, 550);
        this.setLocationRelativeTo(null);
        setVisible(true);

        Image img = Toolkit.getDefaultToolkit().getImage("pic\\finish\\delete.png");
        // ����ImagePanel����ӵ�JFrame
        ImagePanel frame = new ImagePanel(img);
        this.add(frame);

        //��ѡ�򼯺�
        ButtonGroup group = new ButtonGroup();
        JRadioButton option1 = new TransparentRadioButton("����������");
        option1.setBackground(new Color(0, 0, 0, 0)); // ���ñ���Ϊ͸��
        option1.setForeground(Color.white);
        JRadioButton option2 = new TransparentRadioButton("�����˹���");
        option2.setBackground(new Color(0, 0, 0, 0)); // ���ñ���Ϊ͸��
        option2.setForeground(Color.white);
        group.add(option1);
        group.add(option2);



        JLabel goods_name = new JLabel("��Ʒ��:");
        goods_name.setForeground(Color.white);
        goods_name.setFont(new Font("����", Font.PLAIN, 20));//������������壬���ӣ���С
        JTextField goodsName_input = new JTextField(12);

        JLabel staff_id_name = new JLabel("������ID/����:");
        staff_id_name.setForeground(Color.white);
        staff_id_name.setFont(new Font("����", Font.PLAIN, 20));
        JTextField staff_input = new JTextField(10);

        JLabel j1 = new JLabel("(��ܰ���ѣ�����ɾ��Ŷ~)");
        j1.setForeground(Color.white);
        j1.setFont(new Font("����", Font.PLAIN, 12));

        JButton aa = new JButton("ȷ��");
        aa.setFont(new Font("����", Font.PLAIN, 20));
        aa.setBackground(Color.GREEN);
        JButton bb = new JButton("����");
        bb.setFont(new Font("����", Font.PLAIN, 20));
        bb.setBackground(Color.RED);


        aa.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                try {

                    Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                    //���ض�Ӧ��jdbc����
                    String url = "******************************************************************************;";
                    //���������ַ���
                    String user = "sa";//sa��������Ա
                    String password = "123123";//����
                    Connection conn = DriverManager.getConnection(url, user, password);
                    //�������ݿ����Ӷ���
                    Statement st = conn.createStatement();
                    //����SQL���ִ�ж���

                    String a = goodsName_input.getText().trim();

                    String staff = staff_input.getText().trim();
                    if (a.isEmpty() && staff.isEmpty()){
                        JOptionPane.showMessageDialog(null, "����������aaa��");
                    }else {
                        if (!(a.isEmpty())){
                            String s = "(Select * from dbo.cargo where Sname='" + a + "')";
                            ResultSet r = st.executeQuery(s);
                            if (r.next()) {

                                String strSQL = "delete from shelf where Sname = '" + a + "'";
                                int i1 = st.executeUpdate(strSQL);
                                String strSQL1 = "delete from cargo where Sname = '" + a + "'";
                                int i = st.executeUpdate(strSQL1);
                                JOptionPane.showMessageDialog(null, "ɾ����Ʒ�ɹ�Ŷ~");

                            }else {
                                JOptionPane.showMessageDialog(null, "���������Ʒ������","����",JOptionPane.WARNING_MESSAGE);
                            }
                        }
                        if (!(staff.isEmpty())){
                            if (option1.isSelected()) {
                                //��������������
                                String strSQL = "select * from staff where Pname = '" + staff + "'";
                                ResultSet r1 = st.executeQuery(strSQL);
                                if (r1.next()) {
                                    String s1 = r1.getString(1);

                                    String strSQL1 = "delete from supervise where Pid = '" + s1 + "'";
                                    int i1 = st.executeUpdate(strSQL1);
                                    String strSQL2 = "delete from staff where Pname = '" + staff + "'";
                                    int i2 = st.executeUpdate(strSQL2);

                                    JOptionPane.showMessageDialog(null, "���ݸ����˵�����ɾ���ɹ���");
                                }else {
                                    JOptionPane.showMessageDialog(null, "������ĸ�������������","����",JOptionPane.WARNING_MESSAGE);
                                }
                            } else if (option2.isSelected()) {
                                //���������Ǹ����˵Ĺ���
                                String strSQL3 = "select * from staff where Pid = '" + staff + "'";
                                ResultSet r3 = st.executeQuery(strSQL3);
                                if (r3.next()) {

                                    String strSQL4 = "delete from supervise where Pid = '" + staff + "'";
                                    int i4 = st.executeUpdate(strSQL4);
                                    String strSQL5 = "delete from staff where Pid = '" + staff + "'";
                                    int i5 = st.executeUpdate(strSQL5);
                                    JOptionPane.showMessageDialog(null, "���ݸ����˵�idɾ���ɹ���");
                                }else {
                                    JOptionPane.showMessageDialog(null, "������ĸ�����id����","����",JOptionPane.WARNING_MESSAGE);
                                }
                            }
                        }
                    }
                    conn.close();
                } catch (ClassNotFoundException ex) {
                    System.out.println("û���ҵ���Ӧ�����ݿ�������");
                } catch (SQLException ex) {
                    System.out.println("���ݿ����ӻ��������ݿ����ʧ��");
                }
            }
        });


        //��������
        bb.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                goodsName_input.setText("");
                staff_input.setText("");
                option1.setSelected(false);
                option2.setSelected(false);
            }
        });



        frame.setLayout(null);//���ɲ���
        goods_name.setBounds(30, 20, 100, 20);
        goodsName_input.setBounds(130, 20, 150, 25);
        j1.setBounds(10, 480, 450, 15);

        option1.setBounds(30, 120, 150, 20);
        option2.setBounds(30, 150, 150, 20);
        staff_id_name.setBounds(180, 120, 200, 30);
        staff_input.setBounds(180, 160, 100, 25);
        aa.setBounds(100, 420, 100, 30);
        bb.setBounds(300, 420, 100, 30);


        frame.add(aa);
        frame.add(bb);
        frame.add(goods_name);
        frame.add(goodsName_input);
        frame.add(staff_id_name);
        frame.add(staff_input);
        frame.add(option1);
        frame.add(option2);
        frame.add(j1);
    }
}

