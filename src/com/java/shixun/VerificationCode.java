package com.java.shixun ;

import java.awt.*;
import java.awt.Font;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.util.Random;
import javax.swing.JComponent;
public class VerificationCode extends JComponent implements MouseListener {

    private String code;//�����֤���ַ����ͱ���
    private int width,height = 40;
    private  int codelength = 4;
    private Random random = new Random();
    public VerificationCode() {
        width = this.codelength*16+(this.codelength -1)*10;
        setPreferredSize(new Dimension(width,height));
        this.addMouseListener(this);
        setToolTipText("������Ը�����֤��");
    }
    public String getCode(){
        return code;
    }
    public Color getColor(int min, int max){
        if(min > 255){
            min = 255;
        }
        if(max > 255){
            max = 255;
        }
        int red = random.nextInt(max-min)+min;
        int green = random.nextInt(max-min)+min;
        int blue = random.nextInt(max-min)+min;
        return new Color(red,green,blue);
    }

    protected String generateCode() {
        char[] codes = new char[codelength];
        for (int i = 0, len = codes.length; i < len; i++) {
        int randomNum = random.nextInt(3);
        if(randomNum == 0){
            codes[i] = (char)(random.nextInt(26)+65);
        }else if(randomNum == 1){
            codes[i] = (char)(random.nextInt(26)+97);
        }else{
            codes[i] = (char)(random.nextInt(10)+'0');
        }
        }
        this.code = new String(codes);
        return this.code;
    }
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if(this.code == null || this.code.length() !=this.codelength){
            this.code = this.generateCode();
        }
        width = this.codelength * 16 + (this.codelength - 1)*10;
        super.setSize(width,height);
        super.setPreferredSize(new Dimension(width,height));
        Font mFont = new Font("Arial",Font.BOLD,25);
        g.setFont(mFont);
        Graphics2D g2d = (Graphics2D)g;
        g2d.setColor(getColor(200,500));
        g2d.fillRect(0,0,width,height);
        g2d.setColor(getColor(180,200));
        g2d.drawRect(0,0,width-1,height-1);

        int i = 0,len = 150;
        for(;i<len;i++){
            int x  = random.nextInt(width -1 );
            int y = random.nextInt(height -1 );
            int x1 = random.nextInt(width -10 )+10;
            int y1 = random.nextInt(height -4 )+4;
            g2d.setColor(getColor(180,200));
            g2d.drawLine(x,y,x1,y1);
        }
        i = 0;len = this.codelength;
        FontMetrics fm = g2d.getFontMetrics();
        int base = (height - fm.getHeight()) / 2 + fm.getAscent();
        for(;i<len;i++){
            int b = random.nextBoolean() ? 1 : 1;
            g2d.rotate(random.nextInt(10)*0.01*b);
            g2d.setColor(getColor(20,130));
            g2d.drawString(code.charAt(i)+"",16*i+10,base);
        }
    }
    //������һ����֤��
    public void nextCode(){
        generateCode();
        repaint();
    }
    @Override
    public void mouseClicked(MouseEvent e) {
        nextCode();
    }
    @Override
    public void mousePressed(MouseEvent e) {

    }
    @Override
    public void mouseReleased(MouseEvent e) {

    }
    @Override
    public void mouseEntered(MouseEvent e) {

    }
    @Override
    public void mouseExited(MouseEvent e) {

    }
}