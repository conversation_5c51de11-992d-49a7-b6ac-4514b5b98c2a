package com.java.shixun;

import com.java.shixun.finish.ImagePanel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JTextField;

public class update extends JFrame {
    public update() {
        super("�޸���Ϣ");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        this.setSize(500, 550);
        this.setLocationRelativeTo(null);
        setVisible(true);

        Image img = Toolkit.getDefaultToolkit().getImage("pic\\finish\\update.png");
        // ����ImagePanel����ӵ�JFrame
        ImagePanel frame = new ImagePanel(img);
        this.add(frame);

        JLabel j = new JLabel("������ID:");
        j.setFont(new Font("����", Font.PLAIN, 20));//������������壬���ӣ���С
        j.setForeground(Color.white);
        JLabel j1 = new JLabel("����������:");
        j1.setFont(new Font("����", Font.PLAIN, 20));
        j1.setForeground(Color.white);
        JLabel j2 = new JLabel("�绰����:");
        j2.setFont(new Font("����", Font.PLAIN, 20));
        j2.setForeground(Color.white);
        JLabel j3 = new JLabel("����:");
        j3.setFont(new Font("����", Font.PLAIN, 20));
        j3.setForeground(Color.black);

        JLabel j4 = new JLabel("��Ҫ�󶨵��²ֿ��:");
        j4.setFont(new Font("����", Font.PLAIN, 20));
        j4.setForeground(Color.black);
        JLabel j8 = new JLabel("(��ܰ���ѣ�������Ϣ�������д��)");
        j8.setFont(new Font("����", Font.PLAIN, 11));
        j8.setForeground(Color.white);
        JButton aa = new JButton("ȷ��");
        aa.setFont(new Font("����", Font.PLAIN, 20));
        aa.setBackground(Color.GREEN);
        JButton bb = new JButton("����");
        bb.setFont(new Font("����", Font.PLAIN, 20));
        bb.setBackground(Color.RED);
        JTextField c = new JTextField(15);
        JTextField c1 = new JTextField(15);
        JTextField c2 = new JTextField(15);
        JTextField c3 = new JTextField(15);
        JTextField c4 = new JTextField(15);
        aa.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                try {
                    Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                    String url = "******************************************************************************;";
                    String user = "sa";//sa��������Ա
                    String password = "123123";//����
                    Connection conn = DriverManager.getConnection(url, user, password);
                    Statement st = conn.createStatement();
                    String a = c.getText().trim();
                    String a1 = c1.getText().trim();    //����
                    String a2 = c2.getText().trim();    //�绰����
                    String a3 = c3.getText().trim();    //����
                    String a4 = c4.getText().trim();    //�°󶨵Ĳֿ��
                    String s = "select * from staff where Pid = '" + a + "'";
                    ResultSet r = st.executeQuery(s);
                    boolean flag1 = false;
                    if (r.next()) {
                        if (a1 != null) {
                            String strSQL = "update staff set Pname = '" + a1 + "' where Pid = '" + a + "'";
                            int i4 = st.executeUpdate(strSQL);
                        }
                        if (a2 != null) {
                            String strSQL = "update staff set Pphonenumber = '" + a2 + "' where Pid = '" + a + "'";
                            int i5 = st.executeUpdate(strSQL);
                        }
                        if (a3 != null) {
                            String strSQL = "update staff set Ppassword = '" + a3 + "' where Pid = '" + a + "'";
                            int i6 = st.executeUpdate(strSQL);
                        }
                        if (!(a4.isEmpty())) {
                            try {
                                String strSQL = "insert into dbo.supervise(Pid,Cid) values ('" + a + "','" + a4 + "')";
                                int i7 = st.executeUpdate(strSQL);
                            }catch (SQLException ex) {
                                JOptionPane.showMessageDialog(null, "�������Ҫ�󶨵Ĳֿ������/�òֿ��Ѿ��и�������!", "��ʾ��Ϣ", JOptionPane.ERROR_MESSAGE);
                                flag1 = true;
                            }
                        } else if (a4.isEmpty()) {
                            System.out.println("������û���޸İ󶨵Ĳֿ�");
                        }
                        if (a1.isEmpty() && a2.isEmpty() && a3.isEmpty()) {
                            JOptionPane.showMessageDialog(null, "����������������绰���������!");
                        } else if (flag1 == false){
                            JOptionPane.showMessageDialog(null, "�޸ĳɹ�!");
                        }

                    } else {
                        JOptionPane.showMessageDialog(null, "�����˹��Ų����ڣ����������Ƿ���ȷ��");
                    }

                    conn.close();
                } catch (ClassNotFoundException ex) {
                    System.out.println("û���ҵ���Ӧ�����ݿ�������");
                } catch (SQLException ex) {
                    System.out.println("���ݿ����ӻ��������ݿ����ʧ��123");
                }
            }
        });

        //��������
        bb.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                c.setText("");
                c1.setText("");
                c2.setText("");
                c3.setText("");

                c4.setText("");
            }
        });

        frame.setLayout(null);//���ɲ���
        //һ���ֵĴ�С��25
        j.setBounds(20, 30, 130, 20);
        c.setBounds(160, 30, 130, 25);
        j1.setBounds(20, 70, 130, 20);
        c1.setBounds(160, 70, 130, 25);
        j2.setBounds(20, 110, 130, 30);
        c2.setBounds(160, 110, 130, 25);
        j3.setBounds(20, 150, 130, 30);
        c3.setBounds(160, 150, 130, 25);
        j4.setBounds(20, 190, 200, 30);
        c4.setBounds(210, 190, 130, 25);

        aa.setBounds(100, 400, 100, 30);
        bb.setBounds(300, 400, 100, 30);

        j8.setBounds(10, 480, 450, 15);


        frame.add(j);
        frame.add(j1);
        frame.add(j2);
        frame.add(j3);
        frame.add(j4);
        frame.add(c);
        frame.add(c1);
        frame.add(c2);
        frame.add(c3);
        frame.add(c4);
        frame.add(aa);
        frame.add(bb);
        frame.add(j8);
    }

}
