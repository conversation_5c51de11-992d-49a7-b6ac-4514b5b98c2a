package com.java.shixun;
import javax.swing.*;
import javax.swing.plaf.basic.BasicRadioButtonUI;
import java.awt.*;

public class TransparentRadioButton extends JRadioButton {
  public TransparentRadioButton(String text) {
      super(text);
      setUI(new TransparentRadioButtonUI());
      setOpaque(false);
      setContentAreaFilled(false);
      setBorderPainted(true);

  }
  private static class TransparentRadioButtonUI extends BasicRadioButtonUI {
      protected void paintBackground(Graphics g,AbstractButton b) {

      }
  }
}
