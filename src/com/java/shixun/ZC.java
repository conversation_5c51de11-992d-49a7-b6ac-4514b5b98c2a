package com.java.shixun;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.*;
import javax.swing.*;

import com.java.shixun.finish.ImagePanel;
import com.java.shixun.finish.staff_login;

public class ZC extends J<PERSON>rame {
    private VerificationCode vcode = new VerificationCode();
    JTextField user,pass,idd,ph,co;
    JButton register,exit;
    public ZC() {
        super("ע��");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        this.setSize(500,500);
        this.setLocationRelativeTo(null); //�˴��ڽ�������Ļ������
        setVisible(true);
        Image img = Toolkit.getDefaultToolkit().getImage("pic\\finish\\enroll.png");
        // ����ImagePanel����ӵ�JFrame
        ImagePanel frame = new ImagePanel(img);
        this.add(frame);
        JLabel username=new JLabel("����������");
        username.setForeground(Color.white);
        JLabel password=new JLabel("����");
        password.setForeground(Color.white);
        JLabel id=new JLabel("�����˹���");
        id.setForeground(Color.white);
        JLabel phone=new JLabel("�ֻ�����");
        phone.setForeground(Color.white);
        JLabel code=new JLabel("��֤��");
        code.setForeground(Color.white);
        user=new JTextField();
        pass=new JTextField();
        idd=new JTextField();
        ph=new JTextField();
        co=new JTextField();
        register=new JButton("ע  ��");
        register.setForeground(Color.BLACK);
        exit=new JButton("��  ��");
        exit.setForeground(Color.BLACK);

        username.setBounds(20, 46, 125, 40);
        username.setFont(new Font("����",Font.BOLD,17));
        user.setBounds(135,50,120,30);

        password.setBounds(20,100,125,30);
        password.setFont(new Font("����",Font.BOLD,17));
        pass.setBounds(135,100,120,30);

        id.setBounds(20,150,125,30);
        id.setFont(new Font("����",Font.BOLD,17));
        idd.setBounds(135,150,120,30);

        phone.setBounds(20,200,125,30);
        phone.setFont(new Font("����",Font.BOLD,17));
        ph.setBounds(135,200,120,30);

        code.setBounds(20,250,125,30);
        code.setFont(new Font("����",Font.BOLD,17));
        co.setBounds(135,250,120,30);

        vcode.setBounds(112, 300, 100, 40);
        register.setBounds(150,380,70,30);
        exit.setBounds(250,380,70,30);


        frame.setLayout(null);
        frame.add(username);
        frame.add(user);
        frame.add(password);
        frame.add(pass);
        frame.add(id);
        frame.add(idd);
        frame.add(phone);
        frame.add(ph);
        frame.add(code);
        frame.add(co);
        frame.add(register);
        frame.add(exit);
        frame.add(vcode);

        register.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                String user1 = user.getText().trim();
                String pass1 = pass.getText().trim();
                String id1 = idd.getText().trim();
                String ph1 = ph.getText().trim();
                String co1 = co.getText();

                try {
                    Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                    //���ض�Ӧ��jdbc����
                    String url = "******************************************************************************;";
                    //���������ַ���
                    String user = "sa";//sa��������Ա
                    String password = "123123";//����
                    Connection conn = DriverManager.getConnection(url, user, password);
                    //�������ݿ����Ӷ���
                    Statement st = conn.createStatement();

                    if (user1.isEmpty()){
                        JOptionPane.showMessageDialog(null, "����������!", "��ʾ��Ϣ", JOptionPane.WARNING_MESSAGE);
                    }else {
                        //����SQL���ִ�ж���
                        String strSQL = "(Select * from  dbo.staff where Pname='" + user1 + "' )";
                        ResultSet rs = st.executeQuery(strSQL);

                        if (rs.next()){
                            JOptionPane.showMessageDialog(null, "���û����Ѵ���!", "��ʾ��Ϣ", JOptionPane.WARNING_MESSAGE);
                        }else {
                            if (pass1.isEmpty()){
                                JOptionPane.showMessageDialog(null, "����������!", "��ʾ��Ϣ", JOptionPane.WARNING_MESSAGE);
                            }else {
                                if (id1.isEmpty() &&  ph1.isEmpty()){
                                    JOptionPane.showMessageDialog(null, "�������ȫ��Ϣ��лл!", "��ʾ��Ϣ", JOptionPane.WARNING_MESSAGE);
                                }else {
                                    if (co1.isEmpty()) {
                                        JOptionPane.showMessageDialog(null, "��������֤��!", "��ʾ��Ϣ", JOptionPane.WARNING_MESSAGE);
                                    }
                                    else{
                                        if (!isValidCodeRight()) {
                                            JOptionPane.showMessageDialog(null, "��֤�����,����������!", "��ʾ��Ϣ", JOptionPane.WARNING_MESSAGE);
                                        } else {
                                            try{
                                                String sql = "insert into dbo.staff(Pid,Pname,Pphonenumber,Ppassword) values('" + id1 + "','" + user1 + "','" + ph1 + "','" + pass1 + "') ";
                                                int i = st.executeUpdate(sql);
                                                JOptionPane.showMessageDialog(null, "ע��ɹ�");
                                            }
                                            catch (SQLException ex) {
                                                JOptionPane.showMessageDialog(null, "�����˹����ظ�����˲�!", "��ʾ��Ϣ", JOptionPane.WARNING_MESSAGE);
                                            }
                                            conn.close();
                                            //�ر����ݿ�����
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (ClassNotFoundException ex) {
                    System.out.println("û���ҵ���Ӧ�����ݿ�������");
                } catch (SQLException ex) {
                    System.out.println("���ݿ����ӻ��������ݿ����ʧ��");
                }
            }
        });

        exit.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                closeThis();
                JOptionPane.showMessageDialog(null, "��ת�ɹ�", "��ʾ", JOptionPane.WARNING_MESSAGE);
                new staff_login();
            }
        });
    }
    public boolean isValidCodeRight() {

        if (co == null) {
            return false;
        } else if (vcode == null) {
            return true;
        } else if (vcode.getCode().equals(co.getText())) {
            return true;
        } else
            return false;
    }
    public void closeThis()//�رյ�ǰ����
    {
        this.dispose();
    }
}