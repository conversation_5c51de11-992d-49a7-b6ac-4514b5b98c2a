// 主要JavaScript功能

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 检查用户是否已登录
    checkLoginStatus();
    
    // 绑定导航事件
    bindNavigationEvents();
});

// 检查登录状态
function checkLoginStatus() {
    // 从localStorage获取用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    
    // 如果没有用户信息，重定向到登录页
    if (!userInfo.id) {
        window.location.href = '/views/index.html';
    }
    
    // 显示用户名
    const usernameElement = document.getElementById('username');
    if (usernameElement && userInfo.username) {
        usernameElement.textContent = userInfo.username;
    }
}

// 绑定导航事件
function bindNavigationEvents() {
    // 获取所有导航链接
    const navLinks = document.querySelectorAll('.nav-links a');
    
    // 为每个链接添加点击事件
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 获取目标区块ID
            const targetId = this.getAttribute('data-target');
            
            // 隐藏所有区块
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // 显示目标区块
            document.getElementById(targetId).classList.add('active');
            
            // 更新活动链接样式
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// 登出功能
function logout() {
    // 清除本地存储的用户信息
    localStorage.removeItem('userInfo');
    
    // 重定向到登录页
    window.location.href = '/views/index.html';
}