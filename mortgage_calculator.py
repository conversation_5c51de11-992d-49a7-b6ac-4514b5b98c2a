def calculate_monthly_payment(loan_amount, monthly_rate, months):
    """计算每月还款金额"""
    # 温宇博 *********** 22级计算机科学与技术05班
    return loan_amount * (monthly_rate * (1 + monthly_rate) ** months) / ((1 + monthly_rate) ** months - 1)

def calculate_loan_info(loan_amount, annual_rate, years):
    """计算贷款信息"""
    # 温宇博 *********** 22级计算机科学与技术05班
    monthly_rate = annual_rate / 100 / 12
    months = years * 12
    monthly_payment = calculate_monthly_payment(loan_amount * 10000, monthly_rate, months)
    total_payment = monthly_payment * months
    interest = total_payment - loan_amount * 10000
    return monthly_payment, total_payment, interest

def main():
    # 温宇博 *********** 22级计算机科学与技术05班
    valid_terms = [5, 10, 15, 20, 25]
    while True:
        print("\n===== 房贷计算器 =====")
        print("1. 商业贷款")
        print("2. 公积金贷款")
        print("3. 组合贷款")
        print("4. 退出")
        loan_type = input("\n请选择贷款类型: ")
        
        if loan_type == "4":
            print("感谢使用房贷计算器，再见！")
            break
        
        if loan_type not in ["1", "2", "3"]:
            print("无效的选择，请重新输入！")
            continue
        
        try:
            term = int(input("\n请选择贷款期限(年)，可选择: 5、10、15、20、25: "))
            if term not in valid_terms:
                print("请输入合法的贷款期限！")
                continue
        except ValueError:
            print("请输入有效的数字！")
            continue
        
        if loan_type in ["1", "2"]:
            try:
                loan_amount = float(input("\n请输入贷款金额(万元): "))
                if loan_amount <= 0:
                    print("贷款金额必须大于0！")
                    continue
            except ValueError:
                print("请输入有效的金额！")
                continue
            
            if loan_type == "1":
                annual_rate = 4.75 if term <= 5 else 4.90
                loan_name = "商业贷款"
            else:
                annual_rate = 2.75 if term <= 5 else 3.25
                loan_name = "公积金贷款"
            
            monthly_payment, total_payment, interest = calculate_loan_info(loan_amount, annual_rate, term)
            print(f"\n{loan_name}计算结果:")
            print(f"每月月供金额(元): {monthly_payment:.2f}")
            print(f"还款总额(元): {total_payment:.2f}")
            print(f"支付利息(元): {interest:.2f}")
            
        else:
            try:
                business_loan = float(input("\n请输入商业贷款金额(万元): "))
                fund_loan = float(input("请输入公积金贷款金额(万元): "))
                if business_loan <= 0 or fund_loan <= 0:
                    print("贷款金额必须大于0！")
                    continue
            except ValueError:
                print("请输入有效的金额！")
                continue
            
            business_rate = 4.75 if term <= 5 else 4.90
            fund_rate = 2.75 if term <= 5 else 3.25
            
            business_monthly, business_total, business_interest = calculate_loan_info(business_loan, business_rate, term)
            fund_monthly, fund_total, fund_interest = calculate_loan_info(fund_loan, fund_rate, term)
            
            total_monthly = business_monthly + fund_monthly
            total_payment = business_total + fund_total
            total_interest = business_interest + fund_interest
            
            print("\n组合贷款计算结果:")
            print(f"每月月供金额(元): {total_monthly:.2f}")
            print(f"还款总额(元): {total_payment:.2f}")
            print(f"支付利息(元): {total_interest:.2f}")
            print(f"其中商业贷款利息: {business_interest:.2f}元")
            print(f"公积金贷款利息: {fund_interest:.2f}元")

if __name__ == "__main__":
    # 温宇博 *********** 22级计算机科学与技术05班
    main()
