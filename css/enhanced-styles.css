/* 仓库管理系统 - 增强样式表 */

/* 全局样式重置和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #764ba2, #667eea);
}

/* 通用动画类 */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.bounce-in {
    animation: bounceIn 0.8s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* 动画定义 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

/* 通用按钮样式 */
.btn-enhanced {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced:hover {
    transform: translateY(-3px) scale(1.05);
}

/* 按钮变体 */
.btn-primary-enhanced {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary-enhanced:hover {
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.btn-success-enhanced {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-success-enhanced:hover {
    box-shadow: 0 10px 30px rgba(76, 175, 80, 0.4);
}

.btn-info-enhanced {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    color: white;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.btn-info-enhanced:hover {
    box-shadow: 0 10px 30px rgba(79, 172, 254, 0.4);
}

/* 通用卡片样式 */
.card-enhanced {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 18px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255,255,255,0.2);
}

.card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.6s;
}

.card-enhanced:hover::before {
    left: 100%;
}

.card-enhanced:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 50px rgba(0,0,0,0.18);
}

/* 通用表格样式 */
.table-enhanced {
    width: 100%;
    border-collapse: collapse;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    background: white;
}

.table-enhanced thead th {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 18px 15px;
    font-weight: 700;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
}

.table-enhanced tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #f1f3f4;
}

.table-enhanced tbody tr:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    transform: translateX(5px);
}

.table-enhanced tbody td {
    padding: 15px;
    border: none;
    vertical-align: middle;
}

/* 通用输入框样式 */
.input-enhanced {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 0.9rem;
    font-family: inherit;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.9);
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.input-enhanced:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
    background: white;
}

/* 通用徽章样式 */
.badge-enhanced {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

.badge-success {
    background: linear-gradient(135deg, #51cf66, #40c057);
    color: white;
}

.badge-warning {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
    color: #333;
}

.badge-danger {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

.badge-info {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-enhanced {
        padding: 20px;
        margin-bottom: 15px;
    }
    
    .btn-enhanced {
        padding: 10px 20px;
        font-size: 0.8rem;
    }
    
    .table-enhanced {
        font-size: 0.8rem;
    }
    
    .table-enhanced th,
    .table-enhanced td {
        padding: 10px 8px;
    }
}

@media (max-width: 480px) {
    .card-enhanced {
        padding: 15px;
    }
    
    .btn-enhanced {
        padding: 8px 16px;
        font-size: 0.75rem;
    }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip-enhanced {
    position: relative;
    cursor: help;
}

.tooltip-enhanced::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.tooltip-enhanced:hover::after {
    opacity: 1;
}
