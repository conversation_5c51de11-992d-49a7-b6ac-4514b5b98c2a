/* 仓库管理系统通用样式 */

/* 基础样式 */
body {
    margin: 0;
    padding: 0;
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    /* 更现代化的渐变背景 */
    background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
    background-size: 400% 400%;
    animation: gradientAnimation 15s ease infinite;
    background-attachment: fixed;
    position: relative;
    min-height: 100vh;
    scroll-behavior: smooth; /* 平滑滚动效果 */
}

/* 添加背景覆盖层，增强视觉效果 */
body::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.2);
    z-index: -1;
}

/* 渐变动画 */
@keyframes gradientAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 添加全局动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 登录页面样式 */
.container {
    background-color: rgba(255, 255, 255, 0.85);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    padding: 30px;
    width: 380px;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.container:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
}

/* 标题样式 */
h2 {
    text-align: center;
    color: #333;
    margin-bottom: 25px;
    font-size: 28px;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.1);
}

/* 表单元素样式 */
input {
    width: 100%;
    padding: 12px;
    box-sizing: border-box;
    border: 1px solid #ddd;
    border-radius: 6px;
    transition: all 0.3s;
    font-size: 16px;
}

input:focus {
    outline: none;
    border-color: #4caf50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

button {
    width: 100%;
    padding: 12px;
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s;
}

button:hover {
    background-color: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 仪表盘通用样式 */
.section { display: none; }
.section.active { display: block; }

/* 导航样式 */
.nav-links a {
    color: white;
    text-decoration: none;
    padding: 6px 12px;
    border-radius: 4px;
    transition: background 0.3s;
}

.nav-links a.active {
    background: rgba(255,255,255,0.3);
}

.nav-links a:hover {
    background: rgba(255,255,255,0.2);
}