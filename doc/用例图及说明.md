# 仓库管理系统用例图及说明

## 1. 用例图

### 1.1 系统用例图

```mermaid
actorDiagram
    actor 管理员
    actor 普通用户
    
    rectangle 仓库管理系统 {
        usecase UC1 as "用户登录"
        usecase UC2 as "人员管理"
        usecase UC3 as "物品管理"
        usecase UC4 as "仓库管理"
        usecase UC5 as "货架管理"
        usecase UC6 as "库存查询"
        usecase UC7 as "数据刷新"
        
        管理员 --> UC1
        管理员 --> UC2
        管理员 --> UC3
        管理员 --> UC4
        管理员 --> UC5
        管理员 --> UC6
        管理员 --> UC7
        
        普通用户 --> UC1
        普通用户 --> UC6
        普通用户 --> UC7
    }
```

### 1.2 子系统用例图

#### 1.2.1 人员管理子系统

```mermaid
actorDiagram
    actor 管理员
    
    rectangle 人员管理子系统 {
        usecase UC2.1 as "添加人员"
        usecase UC2.2 as "修改人员信息"
        usecase UC2.3 as "删除人员"
        usecase UC2.4 as "查询人员"
        
        管理员 --> UC2.1
        管理员 --> UC2.2
        管理员 --> UC2.3
        管理员 --> UC2.4
    }
```

#### 1.2.2 物品管理子系统

```mermaid
actorDiagram
    actor 管理员
    
    rectangle 物品管理子系统 {
        usecase UC3.1 as "添加物品"
        usecase UC3.2 as "修改物品信息"
        usecase UC3.3 as "删除物品"
        usecase UC3.4 as "查询物品"
        
        管理员 --> UC3.1
        管理员 --> UC3.2
        管理员 --> UC3.3
        管理员 --> UC3.4
    }
```

#### 1.2.3 仓库与货架管理子系统

```mermaid
actorDiagram
    actor 管理员
    actor 普通用户
    
    rectangle 仓库与货架管理子系统 {
        usecase UC4.1 as "仓库信息查询"
        usecase UC5.1 as "货架信息查询"
        usecase UC5.2 as "货架物品关联"
        
        管理员 --> UC4.1
        管理员 --> UC5.1
        管理员 --> UC5.2
        
        普通用户 --> UC4.1
        普通用户 --> UC5.1
    }
```

## 2. 用例描述

### 2.1 用例：用户登录 (UC1)

- **参与者**：管理员、普通用户
- **前置条件**：用户已在系统中注册
- **后置条件**：用户成功登录系统
- **主流程**：
  1. 用户启动系统登录界面
  2. 用户输入用户名和密码
  3. 用户选择角色（管理员或普通用户）
  4. 用户点击登录按钮
  5. 系统验证用户身份
  6. 系统显示对应的管理界面
- **备选流程**：
  5a. 用户名或密码错误
    1. 系统显示错误信息
    2. 返回到登录界面
  
  5b. 所选角色不匹配
    1. 系统显示角色不匹配错误
    2. 返回到登录界面
- **优先级**：高

### 2.2 用例：人员管理 (UC2)

- **参与者**：管理员
- **前置条件**：管理员已登录系统
- **后置条件**：完成人员信息的增删改查
- **主流程**：
  1. 管理员在主界面选择人员管理功能
  2. 系统显示人员列表
  3. 管理员可以执行以下操作：
     - 添加新人员
     - 修改人员信息
     - 删除人员
     - 查询人员
  4. 系统保存操作结果
- **备选流程**：
  3a. 添加人员时工号已存在
    1. 系统显示工号已存在错误
    2. 返回添加人员界面
- **优先级**：高

### 2.3 用例：物品管理 (UC3)

- **参与者**：管理员
- **前置条件**：管理员已登录系统
- **后置条件**：完成物品信息的增删改查
- **主流程**：
  1. 管理员在主界面选择物品管理功能
  2. 系统显示物品列表
  3. 管理员可以执行以下操作：
     - 添加新物品
     - 修改物品信息
     - 删除物品
     - 查询物品
  4. 系统保存操作结果
- **备选流程**：
  3a. 添加物品时物品编号已存在
    1. 系统显示物品编号已存在错误
    2. 返回添加物品界面
- **优先级**：高

### 2.4 用例：仓库管理 (UC4)

- **参与者**：管理员
- **前置条件**：管理员已登录系统
- **后置条件**：完成仓库信息的查询和管理
- **主流程**：
  1. 管理员在主界面查看仓库信息
  2. 系统显示仓库列表
  3. 管理员可以查看仓库详细信息
  4. 管理员可以查询特定仓库
- **备选流程**：
  3a. 仓库信息不存在
    1. 系统显示无结果提示
    2. 管理员可以修改查询条件重新查询
- **优先级**：中

### 2.5 用例：货架管理 (UC5)

- **参与者**：管理员
- **前置条件**：管理员已登录系统，仓库信息已存在
- **后置条件**：完成货架信息的查询和管理
- **主流程**：
  1. 管理员在主界面查看货架信息
  2. 系统显示货架列表
  3. 管理员可以查看货架上的物品信息
  4. 管理员可以查询特定货架
- **备选流程**：
  3a. 货架信息不存在
    1. 系统显示无结果提示
    2. 管理员可以修改查询条件重新查询
- **优先级**：中

### 2.6 用例：库存查询 (UC6)

- **参与者**：管理员、普通用户
- **前置条件**：用户已登录系统
- **后置条件**：显示库存查询结果
- **主流程**：
  1. 用户在主界面输入查询条件
  2. 用户点击查询按钮
  3. 系统根据条件查询库存信息
  4. 系统显示查询结果
- **备选流程**：
  3a. 没有符合条件的结果
    1. 系统显示无结果提示
    2. 用户可以修改查询条件重新查询
- **优先级**：高

### 2.7 用例：数据刷新 (UC7)

- **参与者**：管理员、普通用户
- **前置条件**：用户已登录系统
- **后置条件**：刷新显示最新数据
- **主流程**：
  1. 用户在主界面点击刷新按钮
  2. 系统重新从数据库获取最新数据
  3. 系统更新界面显示
- **备选流程**：
  2a. 数据库连接失败
    1. 系统显示连接错误提示
    2. 用户可以稍后重试
- **优先级**：中

## 3. 用例优先级排序

| 用例ID | 用例名称 | 优先级 | 理由 |
|-------|---------|------|------|
| UC1 | 用户登录 | 高 | 系统基础功能，所有操作的前提 |
| UC2 | 人员管理 | 高 | 仓库管理系统的基础数据管理 |
| UC3 | 物品管理 | 高 | 仓库管理的核心功能 |
| UC6 | 库存查询 | 高 | 系统的核心查询功能 |
| UC4 | 仓库管理 | 中 | 仓库信息的管理功能 |
| UC5 | 货架管理 | 中 | 货架信息的管理功能 |
| UC7 | 数据刷新 | 中 | 保证数据实时性的辅助功能 |

## 4. 用例关系分析

### 4.1 包含关系

- **人员管理** 包含 添加人员、修改人员信息、删除人员、查询人员
- **物品管理** 包含 添加物品、修改物品信息、删除物品、查询物品
- **仓库管理** 包含 查看仓库信息、查询仓库
- **货架管理** 包含 查看货架信息、查询货架、查看货架物品关联

### 4.2 扩展关系

- **数据刷新** 扩展 **库存查询**，当需要查看最新数据时触发
- **货架管理** 扩展 **仓库管理**，提供仓库内部货架的详细信息

### 4.3 泛化关系

- **管理员** 是 **用户** 的特例，拥有更多权限

## 5. 用例实现优先级

根据系统开发的实际需求，建议按照以下顺序实现用例：

1. 用户登录 (UC1)
2. 人员管理 (UC2)
3. 物品管理 (UC3)
4. 仓库管理 (UC4)
5. 货架管理 (UC5)
6. 库存查询 (UC6)
7. 数据刷新 (UC7)

这种实现顺序确保了系统的基础功能优先得到实现，同时为后续功能的开发奠定基础。系统采用Java Swing开发的桌面应用程序，主要功能是对仓库、货架、物品和人员信息进行管理和查询。