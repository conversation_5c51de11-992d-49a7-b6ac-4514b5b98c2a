# 仓库管理系统部署指南

## 1. 概述

本文档提供了仓库管理系统的详细部署步骤，包括环境准备、系统安装、配置和启动等内容，帮助管理员或技术人员快速部署系统。

## 2. 系统要求

### 2.1 硬件要求

| 组件 | 最低配置 | 推荐配置 |
|------|---------|----------|
| CPU | 双核 2.0GHz | 四核 2.5GHz 或更高 |
| 内存 | 4GB | 8GB 或更高 |
| 硬盘 | 50GB 可用空间 | 100GB 或更多可用空间 |
| 网络 | 100Mbps 以太网 | 1000Mbps 以太网 |
| 显示器 | 1366x768 分辨率 | 1920x1080 或更高分辨率 |

### 2.2 软件要求

#### 2.2.1 客户端环境

| 软件 | 版本 | 说明 |
|------|------|------|
| 操作系统 | Windows 7/8/10/11 | 64位操作系统 |
| Java 运行环境 | JRE 8 或更高版本 | 必须安装 |
| 浏览器 | Chrome/Firefox/Edge 最新版 | 可选，用于访问系统文档 |

#### 2.2.2 服务器端环境

| 软件 | 版本 | 说明 |
|------|------|------|
| 操作系统 | Windows Server 2012 或更高版本 | 64位操作系统 |
| 数据库 | SQL Server 2019 | 必须安装 |
| Java 开发工具包 | JDK 8 或更高版本 | 必须安装 |

## 3. 部署步骤

### 3.1 数据库安装与配置

1. **安装 SQL Server 2019**
   - 从 Microsoft 官方网站下载 SQL Server 2019 安装包
   - 运行安装程序，选择「基本」安装类型
   - 按照安装向导完成安装

2. **配置 SQL Server**
   - 打开 SQL Server Configuration Manager
   - 确保 SQL Server 服务已启动
   - 配置 SQL Server 网络设置，启用 TCP/IP 协议
   - 设置 SQL Server 身份验证模式为「SQL Server 和 Windows 身份验证模式」

3. **创建数据库**
   - 打开 SQL Server Management Studio (SSMS)
   - 连接到 SQL Server 实例
   - 创建新数据库，命名为 `WarehouseManagement`
   - 设置数据库恢复模式为「简单」

4. **导入数据库脚本**
   - 在 SSMS 中打开系统提供的数据库脚本文件 `database_script.sql`
   - 执行脚本，创建表结构和初始数据

### 3.2 客户端安装

1. **安装 Java 运行环境**
   - 从 Oracle 官方网站下载 JRE 8 或更高版本
   - 运行安装程序，按照向导完成安装
   - 设置 JAVA_HOME 环境变量，指向 JRE 安装目录
   - 将 `%JAVA_HOME%\bin` 添加到系统 PATH 环境变量

2. **部署客户端应用程序**
   - 将系统提供的客户端安装包 `WarehouseManagement-Client.zip` 解压到指定目录
   - 确保解压后的目录结构完整，包含以下文件和目录：
     - `lib/` 目录：包含所有依赖库
     - `config/` 目录：包含配置文件
     - `WarehouseManagement.jar`：主程序文件
     - `start.bat`：启动脚本

3. **配置客户端**
   - 编辑 `config/application.properties` 文件
   - 设置数据库连接参数：
     ```properties
     db.server=<数据库服务器IP或主机名>
     db.port=1433
     db.name=WarehouseManagement
     db.user=<数据库用户名>
     db.password=<数据库密码>
     ```

### 3.3 系统启动

1. **启动数据库服务**
   - 确保 SQL Server 服务已启动
   - 检查数据库连接是否正常

2. **启动客户端应用程序**
   - 双击 `start.bat` 脚本启动应用程序
   - 或者通过命令行执行：
     ```batch
     java -jar WarehouseManagement.jar
     ```

3. **登录系统**
   - 在登录界面输入默认管理员账号：
     - 用户名：admin
     - 密码：admin123
   - 首次登录后，系统会提示修改默认密码

## 4. 系统配置

### 4.1 基本配置

编辑 `config/application.properties` 文件，可以修改以下配置项：

```properties
# 系统基本配置
system.name=仓库管理系统
system.version=1.0

# 日志配置
log.level=INFO
log.path=logs/
log.file=warehouse.log
log.max.size=10MB
log.max.history=30

# 界面配置
ui.theme=default
ui.language=zh_CN
ui.font.size=12
```

### 4.2 高级配置

#### 4.2.1 连接池配置

```properties
# 数据库连接池配置
db.pool.initial.size=5
db.pool.min.idle=5
db.pool.max.active=20
db.pool.max.wait=60000
```

#### 4.2.2 缓存配置

```properties
# 缓存配置
cache.enabled=true
cache.type=local
cache.expire.time=3600
```

## 5. 系统备份与恢复

### 5.1 数据库备份

1. **使用 SQL Server Management Studio 备份**
   - 在 SSMS 中右键点击数据库，选择「任务」>「备份」
   - 选择备份类型为「完整」
   - 指定备份文件路径和名称
   - 点击「确定」执行备份

2. **使用命令行备份**
   - 打开命令提示符，执行以下命令：
     ```sql
     BACKUP DATABASE WarehouseManagement TO DISK = 'D:\Backup\WarehouseManagement.bak'
     ```

### 5.2 数据库恢复

1. **使用 SQL Server Management Studio 恢复**
   - 在 SSMS 中右键点击数据库，选择「任务」>「还原」>「数据库」
   - 选择源设备，浏览备份文件
   - 选择要还原的备份集
   - 点击「确定」执行恢复

2. **使用命令行恢复**
   - 打开命令提示符，执行以下命令：
     ```sql
     RESTORE DATABASE WarehouseManagement FROM DISK = 'D:\Backup\WarehouseManagement.bak'
     ```

## 6. 常见问题与解决方案

### 6.1 数据库连接问题

**问题**：客户端无法连接到数据库服务器

**解决方案**：
1. 检查数据库服务器是否正常运行
2. 确认数据库连接参数是否正确
3. 检查网络连接是否正常
4. 确认 SQL Server 的 TCP/IP 协议已启用
5. 检查防火墙设置，确保 SQL Server 端口（默认 1433）已开放

### 6.2 客户端启动问题

**问题**：客户端应用程序无法启动

**解决方案**：
1. 确认 Java 运行环境已正确安装
2. 检查 JAVA_HOME 环境变量设置是否正确
3. 确认客户端应用程序文件完整
4. 检查日志文件中的错误信息

### 6.3 登录问题

**问题**：无法使用默认管理员账号登录

**解决方案**：
1. 确认用户名和密码输入正确
2. 检查数据库中的用户表是否包含管理员账号
3. 如果忘记密码，可以通过数据库直接修改密码：
   ```sql
   UPDATE Users SET Password = 'admin123' WHERE Username = 'admin'
   ```

## 7. 安全建议

1. **修改默认密码**：首次登录后立即修改默认管理员密码
2. **定期备份数据**：建立定期备份计划，确保数据安全
3. **更新系统补丁**：定期更新操作系统和数据库系统的安全补丁
4. **限制访问权限**：根据用户角色分配最小必要的访问权限
5. **启用审计日志**：记录重要操作的审计日志，便于追踪问题

## 8. 版本历史

| 版本号 | 日期 | 修改人 | 修改内容 |
|-------|------|-------|----------|
| v1.0 | 2023-12-15 | 系统部署工程师 | 初始版本 |