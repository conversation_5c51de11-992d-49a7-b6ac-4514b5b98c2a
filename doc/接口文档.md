# 仓库管理系统接口文档

## 1. 文档概述

本文档详细描述了仓库管理系统的各个模块接口，包括接口的功能、参数、返回值等信息，为系统开发和维护提供参考。

## 2. 接口分类

仓库管理系统的接口主要分为以下几类：

1. **用户界面接口**：系统与用户交互的界面接口
2. **业务逻辑接口**：系统内部各模块之间的接口
3. **数据访问接口**：系统与数据库之间的接口

## 3. 用户界面接口

### 3.1 登录界面

#### 3.1.1 登录接口

- **功能描述**：验证用户身份并登录系统
- **接口参数**：
  - 用户名（String）：用户的登录名
  - 密码（String）：用户的登录密码
- **返回值**：
  - 登录结果（boolean）：登录是否成功
  - 用户信息（UserInfo）：登录成功后返回的用户信息
- **异常处理**：
  - 用户名不存在
  - 密码错误
  - 账号被锁定

#### 3.1.2 退出登录接口

- **功能描述**：退出当前登录状态
- **接口参数**：无
- **返回值**：
  - 退出结果（boolean）：退出是否成功
- **异常处理**：
  - 会话已过期

### 3.2 管理员界面

#### 3.2.1 人员管理接口

- **功能描述**：管理系统人员信息
- **接口参数**：
  - 操作类型（int）：1-添加，2-修改，3-删除，4-查询
  - 人员信息（StaffInfo）：人员的详细信息
- **返回值**：
  - 操作结果（boolean）：操作是否成功
  - 人员列表（List<StaffInfo>）：查询时返回的人员列表
- **异常处理**：
  - 人员信息不完整
  - 人员已存在
  - 人员不存在

#### 3.2.2 物品管理接口

- **功能描述**：管理系统物品信息
- **接口参数**：
  - 操作类型（int）：1-添加，2-修改，3-删除，4-查询
  - 物品信息（ItemInfo）：物品的详细信息
- **返回值**：
  - 操作结果（boolean）：操作是否成功
  - 物品列表（List<ItemInfo>）：查询时返回的物品列表
- **异常处理**：
  - 物品信息不完整
  - 物品已存在
  - 物品不存在

#### 3.2.3 仓库管理接口

- **功能描述**：管理系统仓库信息
- **接口参数**：
  - 操作类型（int）：1-添加，2-修改，3-删除，4-查询
  - 仓库信息（WarehouseInfo）：仓库的详细信息
- **返回值**：
  - 操作结果（boolean）：操作是否成功
  - 仓库列表（List<WarehouseInfo>）：查询时返回的仓库列表
- **异常处理**：
  - 仓库信息不完整
  - 仓库已存在
  - 仓库不存在

#### 3.2.4 货架管理接口

- **功能描述**：管理系统货架信息
- **接口参数**：
  - 操作类型（int）：1-添加，2-修改，3-删除，4-查询
  - 货架信息（ShelfInfo）：货架的详细信息
- **返回值**：
  - 操作结果（boolean）：操作是否成功
  - 货架列表（List<ShelfInfo>）：查询时返回的货架列表
- **异常处理**：
  - 货架信息不完整
  - 货架已存在
  - 货架不存在

### 3.3 普通用户界面

#### 3.3.1 物品查询接口

- **功能描述**：查询系统物品信息
- **接口参数**：
  - 查询条件（QueryCondition）：查询的条件
- **返回值**：
  - 物品列表（List<ItemInfo>）：符合条件的物品列表
- **异常处理**：
  - 查询条件不合法

#### 3.3.2 仓库查询接口

- **功能描述**：查询系统仓库信息
- **接口参数**：
  - 查询条件（QueryCondition）：查询的条件
- **返回值**：
  - 仓库列表（List<WarehouseInfo>）：符合条件的仓库列表
- **异常处理**：
  - 查询条件不合法

#### 3.3.3 货架查询接口

- **功能描述**：查询系统货架信息
- **接口参数**：
  - 查询条件（QueryCondition）：查询的条件
- **返回值**：
  - 货架列表（List<ShelfInfo>）：符合条件的货架列表
- **异常处理**：
  - 查询条件不合法

## 4. 业务逻辑接口

### 4.1 认证授权模块

#### 4.1.1 用户认证接口

```java
public interface AuthenticationService {
    /**
     * 用户登录认证
     * @param username 用户名
     * @param password 密码
     * @return 认证结果
     */
    public AuthResult authenticate(String username, String password);
    
    /**
     * 用户登出
     * @param userId 用户ID
     * @return 登出结果
     */
    public boolean logout(String userId);
}
```

#### 4.1.2 权限控制接口

```java
public interface AuthorizationService {
    /**
     * 检查用户是否有权限执行某操作
     * @param userId 用户ID
     * @param operation 操作类型
     * @return 是否有权限
     */
    public boolean hasPermission(String userId, String operation);
    
    /**
     * 获取用户角色
     * @param userId 用户ID
     * @return 用户角色
     */
    public String getUserRole(String userId);
}
```

### 4.2 人员管理模块

#### 4.2.1 人员管理接口

```java
public interface StaffService {
    /**
     * 添加人员
     * @param staff 人员信息
     * @return 添加结果
     */
    public boolean addStaff(StaffInfo staff);
    
    /**
     * 更新人员信息
     * @param staff 人员信息
     * @return 更新结果
     */
    public boolean updateStaff(StaffInfo staff);
    
    /**
     * 删除人员
     * @param staffId 人员ID
     * @return 删除结果
     */
    public boolean deleteStaff(String staffId);
    
    /**
     * 查询人员
     * @param condition 查询条件
     * @return 人员列表
     */
    public List<StaffInfo> queryStaff(QueryCondition condition);
}
```

### 4.3 物品管理模块

#### 4.3.1 物品管理接口

```java
public interface ItemService {
    /**
     * 添加物品
     * @param item 物品信息
     * @return 添加结果
     */
    public boolean addItem(ItemInfo item);
    
    /**
     * 更新物品信息
     * @param item 物品信息
     * @return 更新结果
     */
    public boolean updateItem(ItemInfo item);
    
    /**
     * 删除物品
     * @param itemId 物品ID
     * @return 删除结果
     */
    public boolean deleteItem(String itemId);
    
    /**
     * 查询物品
     * @param condition 查询条件
     * @return 物品列表
     */
    public List<ItemInfo> queryItem(QueryCondition condition);
}
```

### 4.4 仓库管理模块

#### 4.4.1 仓库管理接口

```java
public interface WarehouseService {
    /**
     * 添加仓库
     * @param warehouse 仓库信息
     * @return 添加结果
     */
    public boolean addWarehouse(WarehouseInfo warehouse);
    
    /**
     * 更新仓库信息
     * @param warehouse 仓库信息
     * @return 更新结果
     */
    public boolean updateWarehouse(WarehouseInfo warehouse);
    
    /**
     * 删除仓库
     * @param warehouseId 仓库ID
     * @return 删除结果
     */
    public boolean deleteWarehouse(String warehouseId);
    
    /**
     * 查询仓库
     * @param condition 查询条件
     * @return 仓库列表
     */
    public List<WarehouseInfo> queryWarehouse(QueryCondition condition);
}
```

### 4.5 货架管理模块

#### 4.5.1 货架管理接口

```java
public interface ShelfService {
    /**
     * 添加货架
     * @param shelf 货架信息
     * @return 添加结果
     */
    public boolean addShelf(ShelfInfo shelf);
    
    /**
     * 更新货架信息
     * @param shelf 货架信息
     * @return 更新结果
     */
    public boolean updateShelf(ShelfInfo shelf);
    
    /**
     * 删除货架
     * @param shelfId 货架ID
     * @return 删除结果
     */
    public boolean deleteShelf(String shelfId);
    
    /**
     * 查询货架
     * @param condition 查询条件
     * @return 货架列表
     */
    public List<ShelfInfo> queryShelf(QueryCondition condition);
}
```

## 5. 数据访问接口

### 5.1 数据库连接接口

```java
public interface DBConnection {
    /**
     * 获取数据库连接
     * @return 数据库连接
     */
    public Connection getConnection();
    
    /**
     * 关闭数据库连接
     * @param conn 数据库连接
     */
    public void closeConnection(Connection conn);
}
```

### 5.2 数据访问接口

```java
public interface DataAccess<T> {
    /**
     * 插入数据
     * @param entity 实体对象
     * @return 插入结果
     */
    public boolean insert(T entity);
    
    /**
     * 更新数据
     * @param entity 实体对象
     * @return 更新结果
     */
    public boolean update(T entity);
    
    /**
     * 删除数据
     * @param id 主键ID
     * @return 删除结果
     */
    public boolean delete(String id);
    
    /**
     * 查询数据
     * @param id 主键ID
     * @return 实体对象
     */
    public T findById(String id);
    
    /**
     * 条件查询
     * @param condition 查询条件
     * @return 实体对象列表
     */
    public List<T> findByCondition(Map<String, Object> condition);
}
```

## 6. 数据结构定义

### 6.1 用户信息（UserInfo）

```java
public class UserInfo {
    private String userId;      // 用户ID
    private String username;    // 用户名
    private String password;    // 密码（加密后）
    private String role;        // 角色（管理员/普通用户）
    private String status;      // 状态（正常/锁定）
    private Date createTime;    // 创建时间
    private Date updateTime;    // 更新时间
    
    // getter和setter方法
}
```

### 6.2 人员信息（StaffInfo）

```java
public class StaffInfo {
    private String staffId;     // 人员ID
    private String name;        // 姓名
    private String gender;      // 性别
    private String phone;       // 联系电话
    private String email;       // 电子邮箱
    private String department;  // 部门
    private String position;    // 职位
    private Date createTime;    // 创建时间
    private Date updateTime;    // 更新时间
    
    // getter和setter方法
}
```

### 6.3 物品信息（ItemInfo）

```java
public class ItemInfo {
    private String itemId;      // 物品ID
    private String name;        // 物品名称
    private String description; // 物品描述
    private String category;    // 物品分类
    private BigDecimal price;   // 物品价格
    private Integer quantity;   // 库存数量
    private String unit;        // 计量单位
    private Date createTime;    // 创建时间
    private Date updateTime;    // 更新时间
    
    // getter和setter方法
}
```

### 6.4 仓库信息（WarehouseInfo）

```java
public class WarehouseInfo {
    private String warehouseId;  // 仓库ID
    private String name;        // 仓库名称
    private String location;    // 仓库位置
    private Integer capacity;   // 仓库容量
    private String status;      // 仓库状态（正常/维护）
    private Date createTime;    // 创建时间
    private Date updateTime;    // 更新时间
    
    // getter和setter方法
}
```

### 6.5 货架信息（ShelfInfo）

```java
public class ShelfInfo {
    private String shelfId;      // 货架ID
    private String code;         // 货架编号
    private String warehouseId;  // 所属仓库ID
    private Integer capacity;    // 货架容量
    private String status;       // 货架状态（正常/维护）
    private Date createTime;     // 创建时间
    private Date updateTime;     // 更新时间
    
    // getter和setter方法
}
```

### 6.6 查询条件（QueryCondition）

```java
public class QueryCondition {
    private String keyword;      // 关键词
    private String category;     // 分类
    private Date startTime;      // 开始时间
    private Date endTime;        // 结束时间
    private Integer pageNum;     // 页码
    private Integer pageSize;    // 每页大小
    private Map<String, Object> extraParams; // 额外参数
    
    // getter和setter方法
}
```

## 7. 接口调用示例

### 7.1 用户登录示例

```java
// 创建认证服务实例
AuthenticationService authService = new AuthenticationServiceImpl();

// 调用登录接口
String username = "admin";
String password = "password123";
AuthResult result = authService.authenticate(username, password);

// 处理登录结果
if (result.isSuccess()) {
    UserInfo userInfo = result.getUserInfo();
    System.out.println("登录成功，用户角色：" + userInfo.getRole());
} else {
    System.out.println("登录失败：" + result.getMessage());
}
```

### 7.2 查询物品示例

```java
// 创建物品服务实例
ItemService itemService = new ItemServiceImpl();

// 创建查询条件
QueryCondition condition = new QueryCondition();
condition.setKeyword("电脑");
condition.setCategory("电子设备");
condition.setPageNum(1);
condition.setPageSize(10);

// 调用查询接口
List<ItemInfo> items = itemService.queryItem(condition);

// 处理查询结果
System.out.println("查询到" + items.size() + "条记录");
for (ItemInfo item : items) {
    System.out.println(item.getName() + " - " + item.getPrice());
}
```

## 8. 接口安全性

1. **认证与授权**：所有接口调用前必须进行用户认证和权限验证
2. **输入验证**：对所有接口参数进行合法性验证，防止注入攻击
3. **数据加密**：敏感数据（如密码）必须加密存储和传输
4. **日志记录**：记录所有接口调用日志，便于安全审计

## 9. 版本历史

| 版本号 | 日期 | 修改人 | 修改内容 |
|-------|------|-------|----------|
| v1.0 | 2023-12-15 | 系统设计师 | 初始版本 |