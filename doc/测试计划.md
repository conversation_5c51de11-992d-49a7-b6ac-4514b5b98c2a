# 仓库管理系统测试计划

## 1. 测试策略
### 1.1 测试类型
```mermaid
pie
    title 测试类型分布
    "单元测试" : 35
    "集成测试" : 25
    "系统测试" : 30
    "验收测试" : 10
```

### 1.2 测试阶段
| 阶段 | 开始日期 | 结束日期 | 负责人 |
|------|----------|----------|--------|
| 单元测试 | 2023-08-01 | 2023-08-07 | 开发组 |
| 集成测试 | 2023-08-08 | 2023-08-14 | 测试组 |
| 系统测试 | 2023-08-15 | 2023-08-21 | 测试组 |

## 2. 测试用例设计
### 2.1 用户登录模块
```gherkin
Feature: 用户登录
  Scenario: 正常登录
    Given 用户打开登录页面
    When 输入正确用户名"admin"和密码"P@ssw0rd"
    Then 应跳转到仪表盘页面

  Scenario: 错误密码登录
    When 输入错误密码"wrong"
    Then 应显示"密码错误"提示
```

## 3. 测试环境
- 操作系统：Windows 11/Ubuntu 22.04
- 浏览器：Chrome 115+/Firefox 113+
- 数据库：MySQL 8.0
- 测试工具：Jest, Selenium, Postman