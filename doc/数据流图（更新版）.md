# 仓库管理系统数据流图（更新版）

## 1. 数据流图概述

数据流图(Data Flow Diagram, DFD)是一种图形化技术，它描述信息系统中数据的流动和处理过程。本文档提供了仓库管理系统的数据流图，从不同层次展示了系统中数据的流动、处理和存储，与系统架构保持一致。

## 2. 顶层数据流图（0层图）

```
+-------------+          +-------------------+          +-------------+
|             |  数据    |                   |  信息    |             |
|   用户      | -------> |  仓库管理系统     | -------> |   用户      |
|             |          |                   |          |             |
+-------------+          +-------------------+          +-------------+
```

顶层数据流图展示了系统的整体视图，用户（包括管理员和普通用户）向系统输入数据，系统处理后返回信息给用户。

## 3. 一层数据流图

```
                  +---------------------+
                  |                     |
                  |   用户（管理员/     |
                  |   普通用户）        |
                  |                     |
                  +----------+----------+
                             |
           +------------------+------------------+
           |                  |                  |
           v                  v                  v
  +----------------+   +-------------+   +----------------+
  |                |   |             |   |                |
  | 1.认证授权模块 |   | 2.人员管理  |   | 3.物品管理模块 |
  |                |   |    模块     |   |                |
  +----------------+   +-------------+   +----------------+
           |                  |                  |
           |                  |                  |
           v                  v                  v
  +----------------+   +-------------+   +----------------+
  |                |   |             |   |                |
  | 4.仓库管理模块 |   | 5.货架管理  |   | 6.数据存储模块 |
  |                |   |    模块     |   |                |
  +----------------+   +-------------+   +----------------+
```

一层数据流图展示了系统的主要功能模块，与系统架构图中的业务逻辑层保持一致，包括认证授权、人员管理、物品管理、仓库管理、货架管理和数据存储六个模块。

## 4. 二层数据流图

### 4.1 认证授权模块

```
+-------------+         +----------------+         +--------------+
|             | 登录信息 |                | 验证结果 |              |
|   用户      | -------> | 1.1 用户认证   | -------> |   用户       |
|             |         |                |         |              |
+-------------+         +----------------+         +--------------+
                               |
                               | 用户数据
                               v
                        +----------------+
                        |                |
                        | D1 用户数据存储 |
                        |                |
                        +----------------+
                               ^
                               | 用户数据
                               |
+-------------+         +----------------+         +--------------+
|             | 权限请求 |                | 授权结果 |              |
|   用户      | -------> | 1.2 权限控制   | -------> |   用户       |
|             |         |                |         |              |
+-------------+         +----------------+         +--------------+
```

### 4.2 人员管理模块

```
+-------------+         +----------------+         +--------------+
|             | 人员信息 |                | 操作结果 |              |
|   管理员    | -------> | 2.1 人员录入   | -------> |   管理员     |
|             |         |                |         |              |
+-------------+         +----------------+         +--------------+
                               |
                               | 人员数据
                               v
                        +----------------+
                        |                |
                        | D2 人员数据存储 |
                        |                |
                        +----------------+
                               ^
                               | 人员数据
                               |
+-------------+         +----------------+         +--------------+
|             | 查询条件 |                | 人员列表 |              |
|   用户      | -------> | 2.2 人员查询   | -------> |   用户       |
|             |         |                |         |              |
+-------------+         +----------------+         +--------------+
```

### 4.3 物品管理模块

```
+-------------+         +----------------+         +--------------+
|             | 物品信息 |                | 操作结果 |              |
|   管理员    | -------> | 3.1 物品录入   | -------> |   管理员     |
|             |         |                |         |              |
+-------------+         +----------------+         +--------------+
                               |
                               | 物品数据
                               v
                        +----------------+
                        |                |
                        | D3 物品数据存储 |
                        |                |
                        +----------------+
                               ^
                               | 物品数据
                               |
+-------------+         +----------------+         +--------------+
|             | 查询条件 |                | 物品列表 |              |
|   用户      | -------> | 3.2 物品查询   | -------> |   用户       |
|             |         |                |         |              |
+-------------+         +----------------+         +--------------+
```

### 4.4 仓库管理模块

```
+-------------+         +----------------+         +--------------+
|             | 仓库信息 |                | 操作结果 |              |
|   管理员    | -------> | 4.1 仓库录入   | -------> |   管理员     |
|             |         |                |         |              |
+-------------+         +----------------+         +--------------+
                               |
                               | 仓库数据
                               v
                        +----------------+
                        |                |
                        | D4 仓库数据存储 |
                        |                |
                        +----------------+
                               ^
                               | 仓库数据
                               |
+-------------+         +----------------+         +--------------+
|             | 查询条件 |                | 仓库信息 |              |
|   用户      | -------> | 4.2 仓库查询   | -------> |   用户       |
|             |         |                |         |              |
+-------------+         +----------------+         +--------------+
```

### 4.5 货架管理模块

```
+-------------+         +----------------+         +--------------+
|             | 货架信息 |                | 操作结果 |              |
|   管理员    | -------> | 5.1 货架录入   | -------> |   管理员     |
|             |         |                |         |              |
+-------------+         +----------------+         +--------------+
                               |
                               | 货架数据
                               v
                        +----------------+
                        |                |
                        | D5 货架数据存储 |
                        |                |
                        +----------------+
                               ^
                               | 货架数据
                               |
+-------------+         +----------------+         +--------------+
|             | 查询条件 |                | 货架信息 |              |
|   用户      | -------> | 5.2 货架查询   | -------> |   用户       |
|             |         |                |         |              |
+-------------+         +----------------+         +--------------+
```

### 4.6 数据存储模块

```
+----------------+         +----------------+         +----------------+
|                |  数据   |                |  存储   |                |
| 业务逻辑模块    | ------> | 6.1 数据连接   | ------> | SQL Server数据库 |
|                |         |                |         |                |
+----------------+         +----------------+         +----------------+
                                   |
                                   | 查询请求
                                   v
                            +----------------+
                            |                |
                            | 6.2 数据查询   |
                            |                |
                            +----------------+
                                   |
                                   | 查询结果
                                   v
+----------------+         +----------------+         +----------------+
|                |  结果   |                |  数据   |                |
| 业务逻辑模块    | <------ | 6.3 数据返回   | <------ | SQL Server数据库 |
|                |         |                |         |                |
+----------------+         +----------------+         +----------------+
```

## 5. 数据字典

### 5.1 数据流

| 数据流名称 | 描述 | 组成 |
| --- | --- | --- |
| 登录信息 | 用户登录的信息 | 用户名 + 密码 |
| 权限请求 | 用户请求访问的权限 | 用户ID + 操作类型 |
| 人员信息 | 人员的基本信息 | 人员ID + 姓名 + 联系方式 + 角色 |
| 物品信息 | 物品的基本信息 | 物品ID + 物品名称 + 描述 + 分类 + 价格 |
| 仓库信息 | 仓库的基本信息 | 仓库ID + 仓库名称 + 位置 + 容量 |
| 货架信息 | 货架的基本信息 | 货架ID + 货架编号 + 所属仓库ID + 容量 |
| 查询条件 | 查询的条件 | 关键词 + 分类 + 时间范围 |
| 数据 | 业务数据 | 根据具体业务模块而定 |

### 5.2 数据存储

| 数据存储名称 | 描述 | 包含数据 |
| --- | --- | --- |
| D1 用户数据存储 | 存储用户信息 | 用户ID + 用户名 + 密码 + 角色 + 状态 |
| D2 人员数据存储 | 存储人员信息 | 人员ID + 姓名 + 联系方式 + 角色 + 部门 |
| D3 物品数据存储 | 存储物品信息 | 物品ID + 物品名称 + 描述 + 分类 + 价格 + 库存量 |
| D4 仓库数据存储 | 存储仓库信息 | 仓库ID + 仓库名称 + 位置 + 容量 + 状态 |
| D5 货架数据存储 | 存储货架信息 | 货架ID + 货架编号 + 所属仓库ID + 容量 + 状态 |
| SQL Server数据库 | 关系型数据库 | 所有业务数据表 |

### 5.3 处理

| 处理名称 | 描述 | 输入 | 输出 |
| --- | --- | --- | --- |
| 1.1 用户认证 | 验证用户身份 | 用户名 + 密码 | 验证结果 |
| 1.2 权限控制 | 控制用户权限 | 用户ID + 操作类型 | 授权结果 |
| 2.1 人员录入 | 录入人员信息 | 人员信息 | 操作结果 |
| 2.2 人员查询 | 查询人员信息 | 查询条件 | 人员列表 |
| 3.1 物品录入 | 录入物品信息 | 物品信息 | 操作结果 |
| 3.2 物品查询 | 查询物品信息 | 查询条件 | 物品列表 |
| 4.1 仓库录入 | 录入仓库信息 | 仓库信息 | 操作结果 |
| 4.2 仓库查询 | 查询仓库信息 | 查询条件 | 仓库信息 |
| 5.1 货架录入 | 录入货架信息 | 货架信息 | 操作结果 |
| 5.2 货架查询 | 查询货架信息 | 查询条件 | 货架信息 |
| 6.1 数据连接 | 连接数据库 | 连接参数 | 连接状态 |
| 6.2 数据查询 | 查询数据 | 查询语句 | 查询结果 |
| 6.3 数据返回 | 返回查询结果 | 查询结果 | 格式化数据 |

## 6. 数据流与系统架构的映射关系

本数据流图与系统架构图保持一致，具体映射关系如下：

1. **表现层**：对应数据流图中的外部实体"用户"，包括管理员和普通用户
   - Java Swing界面负责接收用户输入和展示系统输出

2. **业务逻辑层**：对应数据流图中的处理过程
   - 认证授权模块：对应数据流图中的"认证授权模块"
   - 人员管理模块：对应数据流图中的"人员管理模块"
   - 物品管理模块：对应数据流图中的"物品管理模块"
   - 仓库管理模块：对应数据流图中的"仓库管理模块"
   - 货架管理模块：对应数据流图中的"货架管理模块"

3. **数据访问层**：对应数据流图中的数据存储
   - 数据库连接：对应数据流图中的"数据存储模块"
   - SQL Server数据库：对应各个数据存储（D1-D5）

## 7. 数据流图说明

1. **数据流向**：数据流图中的箭头表示数据的流向，从源头到目的地。
2. **处理过程**：圆角矩形表示系统中的处理过程，对数据进行转换或处理。
3. **数据存储**：平行线表示数据存储，用于存储系统中的持久数据。
4. **外部实体**：矩形表示系统外部的实体，如用户、管理员等。

## 8. 总结

本文档提供了仓库管理系统的数据流图，从顶层到详细层次展示了系统中数据的流动、处理和存储。数据流图与系统架构保持一致，有助于理解系统的功能和数据处理过程，为系统设计和实现提供指导。

## 9. 版本历史

| 版本号 | 日期 | 修改人 | 修改内容 |
|-------|------|-------|----------|
| v1.0 | 2023-10-15 | 系统设计师 | 初始版本 |
| v1.1 | 2023-11-20 | 系统设计师 | 根据系统架构调整数据流图 |
| v2.0 | 2023-12-15 | 系统设计师 | 更新数据流图，与系统架构保持一致 |