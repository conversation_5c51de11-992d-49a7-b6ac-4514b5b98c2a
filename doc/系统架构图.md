# 仓库管理系统架构图

## 1. 系统整体架构

### 1.1 架构图

```mermaid
flowchart TB
    subgraph 表现层
        UI[用户界面] --> SwingUI[Java Swing界面]
        SwingUI --> AdminUI[管理员界面]
        SwingUI --> StaffUI[普通用户界面]
    end
    
    subgraph 业务逻辑层
        Auth[认证授权模块] --> LoginManager[登录管理]
        StaffManager[人员管理模块] --> StaffCRUD[人员增删改查]
        ItemManager[物品管理模块] --> ItemCRUD[物品增删改查]
        WarehouseManager[仓库管理模块] --> WarehouseCRUD[仓库查询]
        ShelfManager[货架管理模块] --> ShelfCRUD[货架查询]
    end
    
    subgraph 数据访问层
        DBConnection[数据库连接] --> SQLServer[(SQL Server数据库)]
        SQLServer --> StaffDB[(人员数据)]
        SQLServer --> ItemDB[(物品数据)]
        SQLServer --> WarehouseDB[(仓库数据)]
        SQLServer --> ShelfDB[(货架数据)]
        SQLServer --> RelationDB[(关联数据)]
    end
    
    表现层 --> 业务逻辑层
    业务逻辑层 --> 数据访问层
```

### 1.2 架构说明

仓库管理系统采用经典的三层架构设计，包括表现层、业务逻辑层和数据访问层：

1. **表现层**：负责与用户交互，提供基于Java Swing的桌面应用界面。包括：
   - **管理员界面**：提供完整的系统管理功能
   - **普通用户界面**：提供有限的查询和操作功能

2. **业务逻辑层**：系统的核心业务逻辑处理层，包含多个功能模块：
   - **认证授权模块**：处理用户登录和权限控制
   - **人员管理模块**：管理人员信息的增删改查
   - **物品管理模块**：管理物品信息的增删改查
   - **仓库管理模块**：管理仓库信息的查询
   - **货架管理模块**：管理货架信息的查询

3. **数据访问层**：负责数据存储和访问，使用SQL Server数据库：
   - **人员数据**：存储人员信息
   - **物品数据**：存储物品信息
   - **仓库数据**：存储仓库信息
   - **货架数据**：存储货架信息
   - **关联数据**：存储各实体间的关联关系

## 2. 技术栈选择

### 2.1 客户端技术栈

| 技术 | 版本 | 说明 |
|------|------|------|
| Java | 8+ | 编程语言 |
| Java Swing | - | GUI组件库 |
| AWT | - | 抽象窗口工具包 |
| JDBC | - | 数据库连接 |

### 2.2 数据库技术栈

| 技术 | 版本 | 说明 |
|------|------|------|
| SQL Server | 2019 | 关系型数据库 |

### 2.3 开发工具

| 技术 | 版本 | 说明 |
|------|------|------|
| IntelliJ IDEA | - | 集成开发环境 |
| SQL Server Management Studio | - | 数据库管理工具 |

## 3. 部署架构

### 3.1 部署图

```mermaid
flowchart LR
    Client[客户端电脑] --> JavaApp[Java桌面应用程序]
    JavaApp --> SQLServerDB[(SQL Server数据库)]
```

### 3.2 部署说明

仓库管理系统采用简单的C/S（客户端/服务器）架构部署：

1. **客户端**：
   - 在用户电脑上安装Java运行环境(JRE)
   - 部署Java Swing桌面应用程序
   - 用户通过桌面应用程序直接访问系统功能

2. **服务器端**：
   - 部署SQL Server数据库服务器
   - 存储系统所有数据
   - 通过JDBC与客户端应用程序连接

3. **部署要求**：
   - 客户端：Windows操作系统，安装JRE 8或更高版本
   - 服务器端：安装SQL Server 2019或兼容版本
   - 网络：确保客户端能够通过网络连接到数据库服务器

```

## 4. 安全机制

### 4.1 认证与授权

系统采用基本的用户名密码认证机制，并根据用户角色（管理员/普通用户）分配不同的操作权限：

1. **认证机制**：
   - 用户名和密码验证
   - 角色验证（管理员/普通用户）

2. **授权控制**：
   - 管理员：拥有所有功能的操作权限
   - 普通用户：仅拥有查询和数据刷新权限

### 4.2 数据安全

1. **密码安全**：
   - 密码存储在数据库中
   - 数据库访问权限控制

2. **数据备份**：
   - 定期备份数据库
   - 防止数据丢失

## 5. 版本历史

| 版本号 | 日期 | 修改人 | 修改内容 |
|-------|------|-------|----------|
| v1.0 | 2023-10-15 | 系统架构师 | 初始版本 |
| v1.1 | 2023-11-20 | 系统架构师 | 根据实际实现调整架构说明 |