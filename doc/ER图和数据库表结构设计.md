# 仓库管理系统ER图和数据库表结构设计

## 1. ER图

### 1.1 实体关系图

```mermaid
erDiagram
    STAFF ||--o{ SUPERVISE : manages
    STAFF {
        string Pid PK
        string Pname
        string Pphone
        string Ppassword
    }
    
    STASH ||--o{ SHELF : contains
    STASH {
        string Cid PK
        string Cname
        string Clocation
    }
    
    SHELF ||--o{ CARGO : stores
    SHELF {
        string Sid PK
        string Sname
        string Cid FK
    }
    
    CARGO {
        string Gid PK
        string Gname
        string Sname FK
    }
    
    SUPERVISE {
        string Pid FK
        string Cid FK
    }
    
    STAFF ||--o{ SUPERVISE : has
    STASH ||--o{ SUPERVISE : is_managed_by
```

### 1.2 实体关系说明

1. **人员(STAFF)与管理关系(SUPERVISE)**: 一个人员可以管理多个仓库，通过管理关系表记录。

2. **仓库(STASH)与货架(SHELF)**: 一个仓库可以包含多个货架，一个货架只能属于一个仓库。

3. **货架(SHELF)与物品(CARGO)**: 一个货架可以存储多个物品，一个物品只能存储在一个货架上。

4. **仓库(STASH)与管理关系(SUPERVISE)**: 一个仓库可以被多个人员管理，通过管理关系表记录。

## 2. 数据库表结构设计

### 2.1 人员表(staff)

| 字段名 | 数据类型 | 约束 | 说明 |
|-------|---------|------|------|
| Pid | VARCHAR(20) | PRIMARY KEY | 人员工号 |
| Pname | VARCHAR(50) | NOT NULL | 人员姓名 |
| Pphone | VARCHAR(20) | | 联系电话 |
| Ppassword | VARCHAR(50) | NOT NULL | 登录密码 |

**索引设计**:
- PRIMARY KEY (Pid)

### 2.2 仓库表(stash)

| 字段名 | 数据类型 | 约束 | 说明 |
|-------|---------|------|------|
| Cid | VARCHAR(20) | PRIMARY KEY | 仓库编号 |
| Cname | VARCHAR(50) | NOT NULL | 仓库名称 |
| Clocation | VARCHAR(100) | | 仓库位置 |

**索引设计**:
- PRIMARY KEY (Cid)

### 2.3 货架表(shelf)

| 字段名 | 数据类型 | 约束 | 说明 |
|-------|---------|------|------|
| Sid | VARCHAR(20) | PRIMARY KEY | 货架编号 |
| Sname | VARCHAR(50) | NOT NULL | 货架名称 |
| Cid | VARCHAR(20) | FOREIGN KEY | 所属仓库编号 |

**索引设计**:
- PRIMARY KEY (Sid)
- FOREIGN KEY (Cid) REFERENCES stash(Cid)

### 2.4 物品表(cargo)

| 字段名 | 数据类型 | 约束 | 说明 |
|-------|---------|------|------|
| Gid | VARCHAR(20) | PRIMARY KEY | 物品编号 |
| Gname | VARCHAR(50) | NOT NULL | 物品名称 |
| Sname | VARCHAR(50) | FOREIGN KEY | 所属货架名称 |

**索引设计**:
- PRIMARY KEY (Gid)
- FOREIGN KEY (Sname) REFERENCES shelf(Sname)

### 2.5 管理关系表(supervise)

| 字段名 | 数据类型 | 约束 | 说明 |
|-------|---------|------|------|
| Pid | VARCHAR(20) | FOREIGN KEY | 人员工号 |
| Cid | VARCHAR(20) | FOREIGN KEY | 仓库编号 |

**索引设计**:
- PRIMARY KEY (Pid, Cid)
- FOREIGN KEY (Pid) REFERENCES staff(Pid)
- FOREIGN KEY (Cid) REFERENCES stash(Cid)
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 物品ID |
| name | VARCHAR(100) | NOT NULL | 物品名称 |
| description | TEXT | NULL | 物品描述 |
| quantity | INT | NOT NULL, DEFAULT 0 | 库存数量 |
| price | DECIMAL(10,2) | NOT NULL | 物品价格 |
| category_id | INT | NOT NULL, FOREIGN KEY | 分类ID |
| warehouse_id | INT | NOT NULL, FOREIGN KEY | 仓库ID |
| supplier_id | INT | NOT NULL, FOREIGN KEY | 供应商ID |
| alert_threshold | INT | NOT NULL, DEFAULT 10 | 预警阈值 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**:
- PRIMARY KEY (id)
- INDEX idx_category (category_id)
- INDEX idx_warehouse (warehouse_id)
- INDEX idx_supplier (supplier_id)
- INDEX idx_name (name)

### 2.3 库存操作表(inventory_operations)

| 字段名 | 数据类型 | 约束 | 说明 |
|-------|---------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 操作ID |
| user_id | INT | NOT NULL, FOREIGN KEY | 操作用户ID |
| item_id | INT | NOT NULL, FOREIGN KEY | 物品ID |
| operation_type | VARCHAR(20) | NOT NULL | 操作类型(入库/出库) |
| quantity | INT | NOT NULL | 操作数量 |
| operation_time | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 操作时间 |
| remarks | TEXT | NULL | 备注 |

**索引设计**:
- PRIMARY KEY (id)
- INDEX idx_user (user_id)
- INDEX idx_item (item_id)
- INDEX idx_operation_time (operation_time)

### 2.4 分类表(categories)

| 字段名 | 数据类型 | 约束 | 说明 |
|-------|---------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 分类ID |
| name | VARCHAR(50) | NOT NULL, UNIQUE | 分类名称 |
| description | TEXT | NULL | 分类描述 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**:
- PRIMARY KEY (id)
- UNIQUE INDEX idx_name (name)

### 2.5 仓库表(warehouses)

| 字段名 | 数据类型 | 约束 | 说明 |
|-------|---------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 仓库ID |
| name | VARCHAR(100) | NOT NULL, UNIQUE | 仓库名称 |
| location | VARCHAR(255) | NOT NULL | 仓库位置 |
| capacity | DECIMAL(10,2) | NOT NULL | 仓库容量 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**:
- PRIMARY KEY (id)
- UNIQUE INDEX idx_name (name)

### 2.6 供应商表(suppliers)

| 字段名 | 数据类型 | 约束 | 说明 |
|-------|---------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 供应商ID |
| name | VARCHAR(100) | NOT NULL, UNIQUE | 供应商名称 |
| contact_person | VARCHAR(50) | NULL | 联系人 |
| phone | VARCHAR(20) | NULL | 联系电话 |
| email | VARCHAR(100) | NULL | 电子邮箱 |
| address | TEXT | NULL | 地址 |
| created_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**:
- PRIMARY KEY (id)
- UNIQUE INDEX idx_name (name)

## 3. 表关系约束

### 3.1 外键约束

```sql
-- 库存物品表外键
ALTER TABLE inventory_items
ADD CONSTRAINT fk_inventory_category
FOREIGN KEY (category_id) REFERENCES categories(id),
ADD CONSTRAINT fk_inventory_warehouse
FOREIGN KEY (warehouse_id) REFERENCES warehouses(id),
ADD CONSTRAINT fk_inventory_supplier
FOREIGN KEY (supplier_id) REFERENCES suppliers(id);

-- 库存操作表外键
ALTER TABLE inventory_operations
ADD CONSTRAINT fk_operation_user
FOREIGN KEY (user_id) REFERENCES users(id),
ADD CONSTRAINT fk_operation_item
FOREIGN KEY (item_id) REFERENCES inventory_items(id);
```

### 3.2 触发器

```sql
-- 入库触发器：更新库存数量
CREATE TRIGGER after_inbound_operation
AFTER INSERT ON inventory_operations
FOR EACH ROW
BEGIN
    IF NEW.operation_type = '入库' THEN
        UPDATE inventory_items
        SET quantity = quantity + NEW.quantity
        WHERE id = NEW.item_id;
    END IF;
END;

-- 出库触发器：更新库存数量
CREATE TRIGGER after_outbound_operation
AFTER INSERT ON inventory_operations
FOR EACH ROW
BEGIN
    IF NEW.operation_type = '出库' THEN
        UPDATE inventory_items
        SET quantity = quantity - NEW.quantity
        WHERE id = NEW.item_id;
    END IF;
END;
```

## 4. 数据库优化策略

### 4.1 查询优化

1. **合理使用索引**：为常用查询条件创建索引，如物品名称、分类、仓库等。

2. **避免全表扫描**：使用索引字段作为查询条件，避免使用`SELECT *`。

3. **分页查询优化**：使用`LIMIT`和`OFFSET`进行分页查询，避免大量数据一次性返回。

### 4.2 存储优化

1. **字段类型选择**：根据实际需求选择合适的字段类型，如使用`VARCHAR`而不是`CHAR`来存储变长字符串。

2. **归档历史数据**：定期将历史操作记录归档到历史表中，保持主表数据量较小。

3. **使用压缩表**：对于大量历史数据，可以使用表压缩功能减少存储空间。

### 4.3 并发控制

1. **乐观锁**：在库存物品表中添加`version`字段，用于实现乐观锁控制并发更新。

2. **事务隔离级别**：根据业务需求设置合适的事务隔离级别，平衡数据一致性和性能。

3. **分布式锁**：对于分布式部署环境，可以使用Redis实现分布式锁控制并发操作。