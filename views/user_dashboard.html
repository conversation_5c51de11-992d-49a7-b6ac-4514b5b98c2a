<!DOCTYPE html>
<!-- 用户面板页面 -->
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仓库管理系统 - 用户面板</title>
    <link rel="stylesheet" href="../css/styles.css">
    <!-- 引入Chart.js图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 隐藏区块，显示激活区块 */
        .section { display: none; }
        .section.active { display: block; }
        /* 背景与容器 */
        body { margin: 0; padding: 0; font-family: 'Microsoft YaHei', Arial, sans-serif; }
        /* 添加背景透明度覆盖层 */
        body:before { content: ''; position: absolute; top: 0; right: 0; bottom: 0; left: 0; background: rgba(0, 0, 0, 0.2); z-index: -1; }
        .container { background: rgba(255,255,255,0.85); width: 90%; max-width: 1200px; margin: 60px auto; padding: 20px; border-radius: 8px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); animation: fadeIn 0.5s ease-in-out; }
        header { background: linear-gradient(120deg, #3949ab, #2196F3, #00bcd4); color: white; padding: 10px 20px; display: flex; justify-content: space-between; align-items: center; border-radius: 8px 8px 0 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .logo { font-size: 24px; font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.2); }
        .nav-links { display: flex; gap: 15px; }
        .nav-links a { color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; transition: all 0.3s; }
        .nav-links a.active { background: rgba(255,255,255,0.3); transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        .nav-links a:hover { background: rgba(255,255,255,0.2); transform: translateY(-2px); }
        .btn {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            background: linear-gradient(135deg, #45a049, #2e7d32);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px rgba(76, 175, 80, 0.4);
            border-color: rgba(255,255,255,0.2);
        }

        .btn:active {
            transform: translateY(-1px) scale(1.02);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        /* 表格内按钮样式 */
        table .btn, table button {
            padding: 8px 16px;
            font-size: 0.8rem;
            border-radius: 20px;
            background: linear-gradient(135deg, #2196F3, #1976D2);
            border: none;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        table .btn:hover, table button:hover {
            background: linear-gradient(135deg, #1976D2, #1565C0);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }
        .dashboard-cards { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; margin-top: 20px; }
        .card {
            background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
            padding: 25px;
            border-radius: 18px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.6s;
        }

        .card:hover::before {
            left: 100%;
        }

        .card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 50px rgba(0,0,0,0.18);
            border-color: rgba(33, 150, 243, 0.2);
        }

        .card h2, .card h3 {
            color: #2d3748;
            margin-bottom: 20px;
            font-weight: 700;
            text-align: center;
            position: relative;
        }

        .card h2::after, .card h3::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background: linear-gradient(135deg, #2196F3, #4CAF50);
            border-radius: 2px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
            background: white;
        }

        th, td {
            border: none;
            padding: 16px 20px;
            text-align: left;
        }

        th {
            background: linear-gradient(135deg, #2196F3, #4CAF50);
            color: white;
            font-weight: 700;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.03), rgba(76, 175, 80, 0.02));
        }

        tr:hover {
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.08), rgba(76, 175, 80, 0.05));
            transform: translateX(5px);
            transition: all 0.3s ease;
        }

        tbody tr {
            border-bottom: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        tbody tr:last-child {
            border-bottom: none;
        }
        
        /* 图表容器样式 */
        .chart-container { width: 100%; height: 300px; margin: 20px 0; }
        
        /* 使用记录表格样式 */
        .usage-table { margin-top: 20px; }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes slideInFromLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInFromRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 0 0 15px rgba(76, 175, 80, 0.1);
                transform: scale(1.02);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
                transform: scale(1);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        /* 页面加载动画 */
        .container {
            animation: fadeIn 0.8s ease-out;
        }

        .dashboard-cards {
            animation: slideInFromLeft 0.6s ease-out 0.2s both;
        }

        .section {
            animation: fadeIn 0.5s ease-out;
        }

        /* 为不同的卡片添加交错动画 */
        .dashboard-cards .card:nth-child(1) {
            animation: slideInFromLeft 0.6s ease-out 0.1s both;
        }

        .dashboard-cards .card:nth-child(2) {
            animation: fadeIn 0.6s ease-out 0.3s both;
        }

        .dashboard-cards .card:nth-child(3) {
            animation: slideInFromRight 0.6s ease-out 0.5s both;
        }

        /* 输入框和表单样式 */
        input[type="text"], input[type="number"], input[type="email"], input[type="password"], select, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 0.9rem;
            font-family: inherit;
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.9);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        input[type="text"]:focus, input[type="number"]:focus, input[type="email"]:focus,
        input[type="password"]:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #2196F3;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1), 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
            background: white;
        }

        /* 标签样式 */
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3748;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* 表单组样式 */
        .form-group {
            margin-bottom: 20px;
        }

        /* 搜索框特殊样式 */
        #inventorySearch {
            background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
            border: 2px solid rgba(33, 150, 243, 0.2);
            border-radius: 25px;
            padding: 12px 20px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        #inventorySearch:focus {
            border-color: #2196F3;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.15), 0 8px 25px rgba(0,0,0,0.12);
            transform: scale(1.02);
        }

        /* 选择框样式 */
        select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .dashboard-cards {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .card {
                padding: 20px;
            }

            .stat-value {
                font-size: 1.8rem;
            }

            table {
                font-size: 0.8rem;
            }

            th, td {
                padding: 10px 8px;
            }
        }
        
        /* 系统时间显示 */
        .system-time { text-align: center; font-size: 14px; color: #fff; padding: 5px 10px; background: rgba(76, 175, 80, 0.2); border-radius: 20px; margin: 0 15px; animation: pulse 2s infinite; }
        
        /* 统计项目样式 */
        .stats-container {
            display: flex;
            justify-content: space-around;
            margin-top: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px 15px;
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.12), rgba(76, 175, 80, 0.08));
            border-radius: 15px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(33, 150, 243, 0.1);
            cursor: pointer;
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s;
        }

        .stat-item:hover::before {
            left: 100%;
        }

        .stat-item:hover {
            transform: translateY(-8px) scale(1.05);
            box-shadow: 0 15px 35px rgba(33, 150, 243, 0.2);
            border-color: rgba(33, 150, 243, 0.3);
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.18), rgba(76, 175, 80, 0.12));
        }

        .stat-value {
            display: block;
            font-size: 2.2rem;
            font-weight: 800;
            color: #2196F3;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #2196F3, #4CAF50);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-item:hover .stat-value {
            transform: scale(1.1);
            text-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
        }

        .stat-label {
            font-size: 0.9rem;
            color: #5a6c7d;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: color 0.3s ease;
        }

        .stat-item:hover .stat-label {
            color: #2196F3;
        }

        /* 为每个统计项添加不同的动画延迟 */
        .stat-item:nth-child(1) { animation-delay: 0.1s; }
        .stat-item:nth-child(2) { animation-delay: 0.2s; }
        .stat-item:nth-child(3) { animation-delay: 0.3s; }
    </style>
    <script src="../scripts/api-client.js"></script>
</head>
<body>
    <header>
        <div class="logo">仓库管理系统</div>
        <div class="system-time" id="systemTime"></div>
        <div class="nav-links">
            <!-- 导航链接，添加nav-link类 -->
            <a href="javascript:void(0)" class="nav-link active" onclick="showSection('dashboard')">首页</a>
            <a href="javascript:void(0)" class="nav-link" onclick="showSection('inventory'); loadInventory()">我的库存</a>
            <a href="javascript:void(0)" class="nav-link" onclick="showSection('requests'); loadRequests()">申请记录</a>
            <a href="javascript:void(0)" class="nav-link" onclick="showSection('usage'); loadUsageStats()">使用统计</a>
            <a href="javascript:void(0)" class="nav-link" onclick="showSection('profile')">个人设置</a>
            <button class="btn" onclick="logout()">退出登录</button>
        </div>
    </header>
    <div class="container">
        <!-- 仪表盘 -->
        <section id="dashboard" class="section active">
            <div class="dashboard-cards">
                <div class="card">
                    <h2>我的统计</h2>
                    <div class="stats-container">
                        <div class="stat-item">
                            <span class="stat-value" id="stat-applied">0</span>
                            <span class="stat-label">已申请物品</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="stat-approved">0</span>
                            <span class="stat-label">已批准申请</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="stat-pending">0</span>
                            <span class="stat-label">待处理申请</span>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <h2>最近申请</h2>
                    <div id="recent-list"></div>
                    <button class="btn" onclick="showSection('requests')">查看全部</button>
                </div>
                <div class="card">
                    <h2>快捷操作</h2>
                    <button class="btn" onclick="showSection('newRequest')">申请新物品</button>
                    <button class="btn" onclick="showSection('inventory'); loadInventory()">查看库存列表</button>
                    <button class="btn" onclick="showSection('requests'); loadRequests()">历史记录</button>
                    <button class="btn" onclick="alert('请联系管理员 22219010512 温宇博 <EMAIL>')">联系管理员</button>
                </div>
            </div>
        </section>
        <!-- 库存查询 -->
        <section id="inventory" class="section">
            <h2>库存查询</h2>
            <div class="search-container" style="display: flex; margin-bottom: 15px; align-items: center;">
                <input type="text" id="inventorySearch" placeholder="搜索物品..." style="flex: 1; margin-right: 10px; padding: 10px; border-radius: 6px; border: 1px solid #ddd; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                <button class="btn" onclick="searchInventory()" style="white-space: nowrap; padding: 6px 10px; min-width: 40px; flex: 0.1;">搜索</button>
            </div>
            <table><thead><tr><th>ID</th><th>名称</th><th>数量</th><th>操作</th></tr></thead><tbody id="inventoryBody"></tbody></table>
        </section>
        <!-- 新申请 -->
        <section id="newRequest" class="section">
            <h2>新物品申请</h2>
            <div style="margin-bottom: 10px;">
                <label>选择物品：</label>
                <select id="itemSelect" style="width: 200px; padding: 5px;"></select>
            </div>
            <div style="margin-bottom: 10px;">
                <label>申请数量：</label>
                <input type="number" id="quantityInput" min="1" value="1" style="width: 100px; padding: 5px;">
            </div>
            <div style="margin-bottom: 10px;">
                <label>使用用途：</label>
                <input type="text" id="purposeInput" placeholder="请输入使用用途" style="width: 200px; padding: 5px;">
            </div>
            <button class="btn" onclick="submitRequest()">提交申请</button>
        </section>
        <!-- 申请记录 -->
        <section id="requests" class="section">
            <h2>我的申请</h2>
            <button class="btn" onclick="loadRequests()">刷新</button>
            <table><thead><tr><th>申请ID</th><th>物品ID</th><th>数量</th><th>状态</th></tr></thead><tbody id="requestsBody"></tbody></table>
        </section>
        <!-- 个人设置 -->
        <section id="profile" class="section">
            <h2>个人设置</h2>
            <input type="text" id="profileUsername" disabled><br>
            <input type="password" id="profilePassword" placeholder="新密码"><br>
            <input type="password" id="profileConfirm" placeholder="确认密码"><button class="btn" onclick="updateProfile()">保存</button>
        </section>
        
        <!-- 使用统计 -->
        <section id="usage" class="section">
            <h2>物品使用统计</h2>
            <div class="dashboard-cards">
                <div class="card">
                    <h3>使用趋势</h3>
                    <div class="chart-container">
                        <canvas id="usageChart"></canvas>
                    </div>
                </div>
                <div class="card">
                    <h3>物品分类统计</h3>
                    <div class="chart-container">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="card usage-table">
                <h3>最近使用记录</h3>
                <table>
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>物品名称</th>
                            <th>数量</th>
                            <th>用途</th>
                        </tr>
                    </thead>
                    <tbody id="usageRecordsBody"></tbody>
                </table>
            </div>
        </section>
    </div>
    <script>
        // 退出登录
        function logout() { localStorage.removeItem('user'); location.href='index.html'; }

        // 显示增强的消息通知
        function showMessage(message, type = 'info') {
            // 创建通知容器（如果不存在）
            let notificationContainer = document.getElementById('notification-container');
            if (!notificationContainer) {
                notificationContainer = document.createElement('div');
                notificationContainer.id = 'notification-container';
                notificationContainer.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    pointer-events: none;
                `;
                document.body.appendChild(notificationContainer);
            }

            // 创建通知元素
            const notification = document.createElement('div');
            const icons = {
                success: '✓',
                error: '✗',
                warning: '⚠',
                info: 'ℹ'
            };

            const colors = {
                success: { bg: 'linear-gradient(135deg, #51cf66, #40c057)', border: '#40c057' },
                error: { bg: 'linear-gradient(135deg, #ff6b6b, #ee5a52)', border: '#ee5a52' },
                warning: { bg: 'linear-gradient(135deg, #feca57, #ff9ff3)', border: '#feca57' },
                info: { bg: 'linear-gradient(135deg, #4facfe, #00f2fe)', border: '#4facfe' }
            };

            notification.style.cssText = `
                background: ${colors[type]?.bg || colors.info.bg};
                color: white;
                padding: 16px 20px;
                margin-bottom: 10px;
                border-radius: 12px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                font-size: 14px;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 10px;
                min-width: 300px;
                max-width: 400px;
                transform: translateX(400px);
                transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                pointer-events: auto;
                cursor: pointer;
                border-left: 4px solid ${colors[type]?.border || colors.info.border};
            `;

            notification.innerHTML = `
                <span style="font-size: 18px; font-weight: bold;">${icons[type] || icons.info}</span>
                <span style="flex: 1;">${message}</span>
                <span style="font-size: 18px; opacity: 0.7; cursor: pointer;" onclick="this.parentElement.remove()">×</span>
            `;

            notificationContainer.appendChild(notification);

            // 动画进入
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 10);

            // 悬停暂停自动消失
            let autoRemoveTimer = setTimeout(() => {
                removeNotification(notification);
            }, 4000);

            notification.addEventListener('mouseenter', () => {
                clearTimeout(autoRemoveTimer);
            });

            notification.addEventListener('mouseleave', () => {
                autoRemoveTimer = setTimeout(() => {
                    removeNotification(notification);
                }, 2000);
            });

            // 点击关闭
            notification.addEventListener('click', () => {
                removeNotification(notification);
            });

            function removeNotification(element) {
                element.style.transform = 'translateX(400px)';
                element.style.opacity = '0';
                setTimeout(() => {
                    if (element.parentNode) {
                        element.parentNode.removeChild(element);
                    }
                }, 400);
            }
        }
        // 切换区块
        function showSection(id) {
            // 隐藏所有区块
            document.querySelectorAll('.section').forEach(s => s.classList.remove('active'));
            // 移除所有导航高亮
            document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
            // 显示目标区块
            const sec = document.getElementById(id);
            if (sec) sec.classList.add('active');
            // 高亮对应导航
            const nav = document.querySelector(`.nav-links .nav-link[onclick*="${id}"]`);
            if (nav) nav.classList.add('active');
            // 保存当前区块
            localStorage.setItem('activeSection', id);
            
            // 根据区块ID加载相应数据，但不包括申请记录（requests）
            if (id === 'inventory') loadInventory();
            if (id === 'usage') loadUsageStats();
        }
        // 加载库存
        async function loadInventory() {
            try {
                const result = await apiClient.getInventory();
                if (result.success) {
                    const body = document.getElementById('inventoryBody');
                    body.innerHTML = '';
                    result.items.forEach(item => {
                        body.innerHTML += `
                            <tr>
                                <td>${item.id}</td>
                                <td>${item.name}</td>
                                <td>${item.quantity}</td>
                                <td>
                                    <button onclick="showItemDetails(${item.id},'${item.name}',${item.quantity},'${item.category}','${item.supplier}')">
                                        详情
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                }
            } catch (error) {
                console.error('加载库存失败:', error);
            }
        }
        // 搜索库存
        async function searchInventory() {
            const kw = document.getElementById('inventorySearch').value.trim();
            try {
                const result = await apiClient.getInventory();
                if (result.success) {
                    const body = document.getElementById('inventoryBody');
                    body.innerHTML = '';

                    let items = result.items;
                    if (kw) {
                        items = items.filter(item =>
                            item.name.toLowerCase().includes(kw.toLowerCase()) ||
                            (item.category && item.category.toLowerCase().includes(kw.toLowerCase()))
                        );
                    }

                    items.forEach(item => {
                        body.innerHTML += `
                            <tr>
                                <td>${item.id}</td>
                                <td>${item.name}</td>
                                <td>${item.quantity}</td>
                                <td>
                                    <button onclick="showItemDetails(${item.id},'${item.name}',${item.quantity},'${item.category}','${item.supplier}')">
                                        详情
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                }
            } catch (error) {
                console.error('搜索库存失败:', error);
            }
        }
        // 提交申请
        async function submitRequest() {
            const itemSelect = document.getElementById('itemSelect');
            const quantityInput = document.getElementById('quantityInput');
            const purposeInput = document.getElementById('purposeInput');

            const itemValue = itemSelect.value;
            const quantity = parseInt(quantityInput.value);
            const purpose = purposeInput ? purposeInput.value : '日常使用';

            if (!itemValue || !quantity || quantity <= 0) {
                alert('请选择物品并输入有效数量');
                return;
            }

            // 从选项文本中提取物品名称
            const selectedOption = itemSelect.options[itemSelect.selectedIndex];
            const itemName = selectedOption.text.split('-')[1] || selectedOption.text;

            try {
                const result = await apiClient.submitRequest({
                    item: itemName,
                    quantity: quantity,
                    purpose: purpose,
                    type: 'outbound'
                });

                if (result.success) {
                    showMessage('申请提交成功，等待审批', 'success');
                    itemSelect.value = '';
                    quantityInput.value = '';
                    if (purposeInput) purposeInput.value = '';
                    loadRequests(true);
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                console.error('提交申请失败:', error);
                showMessage('提交申请失败，请检查网络连接', 'error');
            }
        }
        // 加载申请记录
        function loadRequests(showSuccessMessage = false) {
            // 获取并禁用刷新按钮
            const refreshBtn = document.querySelector('#requests .btn[onclick*="loadRequests"]');
            if (refreshBtn) {
                refreshBtn.disabled = true;
                refreshBtn.textContent = '刷新中...';
                refreshBtn.style.opacity = '0.7';
                refreshBtn.style.cursor = 'not-allowed';
            }
            
            // 定义恢复按钮状态的函数
            const restoreButtonState = () => {
                if (refreshBtn) {
                    refreshBtn.disabled = false;
                    refreshBtn.textContent = '刷新';
                    refreshBtn.style.opacity = '1';
                    refreshBtn.style.cursor = 'pointer';
                }
            };
            
            try {
                // 获取用户数据
                const u = JSON.parse(localStorage.getItem('user') || '{}');
                if (!u.id) {
                    alert('用户信息无效，请重新登录');
                    restoreButtonState();
                    return;
                }
                
                const result = await apiClient.getRequests();
                const b = document.getElementById('requestsBody');
                b.innerHTML = '';

                if (result && result.success && Array.isArray(result.requests)) {
                    // 过滤当前用户的申请
                    const userRequests = result.requests.filter(req => req.user === u.username);

                    userRequests.forEach(request => {
                        let statusBadge = '';
                        switch(request.status) {
                            case '待审批':
                                statusBadge = '<span style="color: orange;">待审批</span>';
                                break;
                            case '已批准':
                                statusBadge = '<span style="color: green;">已批准</span>';
                                break;
                            case '已拒绝':
                                statusBadge = '<span style="color: red;">已拒绝</span>';
                                break;
                            default:
                                statusBadge = request.status;
                        }

                        b.innerHTML += `
                            <tr>
                                <td>${request.id}</td>
                                <td>${request.item}</td>
                                <td>${request.quantity}</td>
                                <td>${statusBadge}</td>
                            </tr>
                        `;
                    });

                    // 最近申请列表和统计
                    document.getElementById('stat-applied').textContent = userRequests.length;
                    const pending = userRequests.filter(x => x.status === '待审批').length;
                    document.getElementById('stat-pending').textContent = pending;
                    document.getElementById('recent-list').innerHTML = userRequests.slice(-3).map(x => `<div>${x.item} - ${x.status}</div>`).join('');

                    // 显示成功消息（如果需要）
                    if (showSuccessMessage) {
                        showMessage('数据刷新完成！', 'success');
                    }
                } else {
                    console.error('获取申请记录失败');
                    b.innerHTML = '<tr><td colspan="4">获取数据失败，请重试</td></tr>';
                }
            } catch (error) {
                console.error('加载申请记录时出错:', error);
                document.getElementById('requestsBody').innerHTML = '<tr><td colspan="4">加载数据时出错，请重试</td></tr>';
            } finally {
                // 无论成功或失败，都确保恢复按钮状态
                setTimeout(restoreButtonState, 500); // 延迟500毫秒恢复按钮，提供更好的视觉反馈
            }
        }
        
        // 加载使用统计数据
        function loadUsageStats() {
            try {
                // 获取用户数据
                const u = JSON.parse(localStorage.getItem('user') || '{}');
                if (!u.id) {
                    alert('用户信息无效，请重新登录');
                    return;
                }
                
                // 模拟获取使用记录数据
                const usageData = mockGetUsageData(u.id);
                
                // 渲染使用记录表格
                renderUsageRecords(usageData.records);
                
                // 渲染使用趋势图表
                renderUsageChart(usageData.trends);
                
                // 渲染物品分类统计图表
                renderCategoryChart(usageData.categories);
                
            } catch (error) {
                console.error('加载使用统计数据时出错:', error);
                alert('加载使用统计数据失败，请重试');
            }
        }
        
        // 渲染使用记录表格
        function renderUsageRecords(records) {
            const tbody = document.getElementById('usageRecordsBody');
            tbody.innerHTML = '';
            
            if (records && Array.isArray(records) && records.length > 0) {
                records.forEach(record => {
                    tbody.innerHTML += `
                        <tr>
                            <td>${record.date}</td>
                            <td>${record.itemName}</td>
                            <td>${record.quantity}</td>
                            <td>${record.purpose}</td>
                        </tr>
                    `;
                });
            } else {
                tbody.innerHTML = '<tr><td colspan="4">暂无使用记录</td></tr>';
            }
        }
        
        // 渲染使用趋势图表
        function renderUsageChart(trends) {
            const ctx = document.getElementById('usageChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.usageChartInstance) {
                window.usageChartInstance.destroy();
            }
            
            // 创建新的图表实例
            window.usageChartInstance = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: trends.map(t => t.month),
                    datasets: [{
                        label: '物品使用量',
                        data: trends.map(t => t.count),
                        borderColor: 'rgba(75, 192, 192, 1)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '近6个月物品使用趋势'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        },
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '使用数量'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '月份'
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeOutQuart'
                    }
                }
            });
        }
        
        // 渲染物品分类统计图表
        function renderCategoryChart(categories) {
            const ctx = document.getElementById('categoryChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.categoryChartInstance) {
                window.categoryChartInstance.destroy();
            }
            
            // 创建新的图表实例
            window.categoryChartInstance = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: categories.map(c => c.name),
                    datasets: [{
                        data: categories.map(c => c.count),
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '物品分类使用统计'
                        },
                        legend: {
                            position: 'right',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    animation: {
                        animateRotate: true,
                        animateScale: true
                    }
                }
            });
        }
        
        // 模拟获取使用数据的函数
        function mockGetUsageData(userId) {
            // 模拟使用记录数据
            const records = [
                { date: '2023-06-15', itemName: '笔记本电脑', quantity: 1, purpose: '项目开发' },
                { date: '2023-06-10', itemName: '显示器', quantity: 2, purpose: '办公使用' },
                { date: '2023-06-05', itemName: '键盘', quantity: 5, purpose: '团队使用' },
                { date: '2023-05-28', itemName: '鼠标', quantity: 5, purpose: '团队使用' },
                { date: '2023-05-20', itemName: '打印纸', quantity: 2, purpose: '文档打印' },
                { date: '2023-05-15', itemName: '硬盘', quantity: 3, purpose: '数据备份' },
                { date: '2023-05-10', itemName: 'U盘', quantity: 10, purpose: '资料分发' },
                { date: '2023-05-05', itemName: '投影仪', quantity: 1, purpose: '会议演示' }
            ];
            
            // 模拟使用趋势数据
            const trends = [
                { month: '1月', count: 12 },
                { month: '2月', count: 19 },
                { month: '3月', count: 15 },
                { month: '4月', count: 25 },
                { month: '5月', count: 22 },
                { month: '6月', count: 30 }
            ];
            
            // 模拟物品分类数据
            const categories = [
                { name: '电子设备', count: 45 },
                { name: '办公用品', count: 30 },
                { name: '耗材', count: 15 },
                { name: '工具', count: 10 },
                { name: '其他', count: 5 }
            ];
            
            return { records, trends, categories };
        }
        
        // 更新系统时间
        function updateSystemTime() {
            const now = new Date();
            const options = { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric', 
                weekday: 'long',
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit' 
            };
            document.getElementById('systemTime').textContent = now.toLocaleDateString('zh-CN', options);
        }
        
        // 查看详情
        function showItemDetails(id,name,qty) { alert(`物品详情\nID:${id}\n名称:${name}\n数量:${qty}`); }
        // 更新个人设置
        function updateProfile() {
            const p=document.getElementById('profilePassword').value;
            const c=document.getElementById('profileConfirm').value;
            if(p && p!==c) return alert('两次密码不一致');
            alert('个人信息已保存（模拟）');
        }
        // 初始化
        document.addEventListener('DOMContentLoaded', async () => {
            const u = JSON.parse(localStorage.getItem('user') || '{}');
            if (!u.username) return location.href = 'index.html';

            document.getElementById('profileUsername').value = u.username;

            // 填充申请下拉与统计
            try {
                const result = await apiClient.getInventory();
                if (result.success) {
                    const select = document.getElementById('itemSelect');
                    select.innerHTML = '<option value="">请选择物品</option>';
                    result.items.forEach(item => {
                        select.append(new Option(`${item.id}-${item.name}`, item.id));
                    });
                    document.getElementById('stat-inventory').textContent = result.items.reduce((s, i) => s + i.quantity, 0);
                }
            } catch (error) {
                console.error('初始化库存数据失败:', error);
            }

            // 初始化系统时间并设置定时更新
            updateSystemTime();
            setInterval(updateSystemTime, 1000);

            // 显示上次区块并加载数据
            const last = localStorage.getItem('activeSection') || 'dashboard';
            showSection(last);
            // 注意：申请记录不再自动加载，需要用户点击刷新按钮
        });
    </script>
</body>
</html>