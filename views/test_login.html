<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-card h3 {
            margin-top: 0;
            color: #333;
        }
        .login-form {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .login-form input, .login-form select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .login-form button {
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .login-form button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .quick-login {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        .quick-login button {
            flex: 1;
            padding: 8px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>仓库管理系统 - 登录测试</h1>
    
    <div class="test-card">
        <h3>手动登录测试</h3>
        <div class="login-form">
            <input type="text" id="username" placeholder="用户名" value="sysadmin">
            <input type="password" id="password" placeholder="密码" value="sys123">
            <select id="role">
                <option value="system_admin">系统管理员</option>
                <option value="business_admin">业务管理员</option>
                <option value="user">普通用户</option>
            </select>
            <button onclick="testLogin()">登录测试</button>
        </div>
        
        <div class="quick-login">
            <button onclick="quickLogin('sysadmin', 'sys123', 'system_admin')">系统管理员</button>
            <button onclick="quickLogin('bizadmin', 'biz123', 'business_admin')">业务管理员</button>
            <button onclick="quickLogin('user', 'password', 'user')">普通用户</button>
        </div>
        
        <div id="loginResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-card">
        <h3>API连接测试</h3>
        <button onclick="testConnection()">测试服务器连接</button>
        <div id="connectionResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-card">
        <h3>数据测试</h3>
        <button onclick="testInventory()">测试库存数据</button>
        <button onclick="testRequests()">测试申请数据</button>
        <button onclick="testUsers()">测试用户数据</button>
        <div id="dataResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-card">
        <h3>页面跳转测试</h3>
        <button onclick="goToPage('index.html')">登录页面</button>
        <button onclick="goToPage('system_admin_dashboard.html')">系统管理员</button>
        <button onclick="goToPage('business_admin_dashboard.html')">业务管理员</button>
        <button onclick="goToPage('user_dashboard.html')">普通用户</button>
    </div>

    <script src="../scripts/api-client.js"></script>
    <script>
        // 测试登录功能
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const role = document.getElementById('role').value;
            
            const resultDiv = document.getElementById('loginResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试登录...';
            resultDiv.className = 'result';
            
            try {
                const result = await apiClient.login(username, password, role);
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `登录成功！\n用户信息：\n${JSON.stringify(result.user, null, 2)}`;
                    
                    // 3秒后跳转到对应页面
                    setTimeout(() => {
                        redirectToUserPage(result.user.role);
                    }, 3000);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `登录失败：${result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `登录错误：${error.message}`;
            }
        }
        
        // 快速登录
        async function quickLogin(username, password, role) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            document.getElementById('role').value = role;
            await testLogin();
        }
        
        // 根据角色跳转页面
        function redirectToUserPage(role) {
            switch(role) {
                case 'system_admin':
                    window.location.href = 'system_admin_dashboard.html';
                    break;
                case 'business_admin':
                    window.location.href = 'business_admin_dashboard.html';
                    break;
                case 'user':
                    window.location.href = 'user_dashboard.html';
                    break;
                default:
                    alert('未知角色：' + role);
            }
        }
        
        // 测试服务器连接
        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试连接...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch('http://localhost:3000/api/stats');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `服务器连接正常！\n响应数据：\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `服务器响应错误：${response.status} ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `连接失败：${error.message}\n请确保服务器正在运行在 http://localhost:3000`;
            }
        }
        
        // 测试库存数据
        async function testInventory() {
            const resultDiv = document.getElementById('dataResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在获取库存数据...';
            resultDiv.className = 'result';
            
            try {
                const result = await apiClient.getInventory();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `库存数据获取成功！\n共 ${result.items.length} 个物品：\n${JSON.stringify(result.items, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `获取库存数据失败：${result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `获取库存数据错误：${error.message}`;
            }
        }
        
        // 测试申请数据
        async function testRequests() {
            const resultDiv = document.getElementById('dataResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在获取申请数据...';
            resultDiv.className = 'result';
            
            try {
                const result = await apiClient.getRequests();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `申请数据获取成功！\n共 ${result.requests.length} 个申请：\n${JSON.stringify(result.requests, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `获取申请数据失败：${result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `获取申请数据错误：${error.message}`;
            }
        }
        
        // 测试用户数据
        async function testUsers() {
            const resultDiv = document.getElementById('dataResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在获取用户数据...';
            resultDiv.className = 'result';
            
            try {
                const result = await apiClient.getAllUsers();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `用户数据获取成功！\n共 ${result.users.length} 个用户：\n${JSON.stringify(result.users, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `获取用户数据失败：${result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `获取用户数据错误：${error.message}`;
            }
        }
        
        // 跳转到指定页面
        function goToPage(page) {
            window.location.href = page;
        }
        
        // 页面加载时的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('登录测试页面已加载');
            console.log('当前服务器地址：http://localhost:3000');
        });
    </script>
</body>
</html>
