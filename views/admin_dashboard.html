<!DOCTYPE html>
<!-- 网页声明为HTML5 -->
<html lang="zh-CN">
<!-- 指定语言为中文 -->
<head>
    <!-- 头部元素 -->
    <meta charset="UTF-8">
    <!-- 字符编码为UTF-8 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 视口设置，确保在不同设备上正常显示 -->
    <title>管理员仪表盘 - 仓库管理系统</title>
    <!-- 页面标题 -->
    <script src="../scripts/mock-api.js"></script>
    <!-- 引入模拟API脚本 -->
    <link rel="stylesheet" href="../css/styles.css">
    <!-- 引入样式表 -->
    <style>
        /* CSS样式部分 */
        /* 系统时间显示样式 */
        .system-time {
            text-align: center;
            font-size: 16px;
            color: #fff;
            font-weight: bold;
            padding: 8px 15px;
            background: rgba(76, 175, 80, 0.2);
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin: 0 15px;
            animation: pulse 2s infinite;
            transition: all 0.3s ease;
        }
        
        .system-time:hover {
            background: rgba(76, 175, 80, 0.3);
            transform: scale(1.02);
        }
        
        * {
            /* 全局选择器 */
            margin: 0;
            /* 外边距 */
            padding: 0;
            /* 内边距 */
            box-sizing: border-box;
            /* 盒模型计算方式 */
        }
        
        body {
            /* 使用全局CSS中定义的渐变背景 */
            /* 设置字体及文本样式 */
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            position: relative;
            animation: fadeIn 1.2s ease-in-out; /* 添加淡入动画效果 */
        }
        
        /* 添加背景透明度覆盖层 */
        body:before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background: rgba(0, 0, 0, 0.2);
            z-index: -1;
            backdrop-filter: blur(2px); /* 添加模糊效果 */
            -webkit-backdrop-filter: blur(2px); /* Safari支持 */
        }
        
        /* 库存预警样式 */
        .warning-link {
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .warning-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background-color: #ff5252;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            margin-left: 5px;
            animation: pulse 1.5s infinite;
        }
        
        /* 预警项目样式 */
        .warning-item {
            border-left: 4px solid #ff5252;
            padding-left: 10px;
            margin-bottom: 10px;
            background-color: rgba(255, 82, 82, 0.1);
            border-radius: 4px;
            padding: 10px;
            transition: all 0.3s ease;
        }
        
        .warning-item:hover {
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        /* 添加动画效果 */
        @keyframes fadeIn {
            /* 淡入动画定义 */
            from { opacity: 0; }
            /* 初始状态 */
            to { opacity: 1; }
            /* 最终状态 */
        }
        
        @keyframes floatIn {
            /* 浮入动画定义 */
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes pulse {
            /* 脉冲动画定义 */
            0% { box-shadow: 0 0 5px rgba(76, 175, 80, 0.5); }
            100% { box-shadow: 0 0 20px rgba(76, 175, 80, 0.8); }
        }
        
        .container {
            /* 容器样式，半透明背景与圆角 */
            background: rgba(255,255,255,0.85);
            border-radius: 8px;
            width: 95%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            /* 页头样式 */
            background: linear-gradient(120deg, #1e88e5, #512da8);
            /* 渐变背景 */
            color: white;
            /* 文本颜色 */
            padding: 1rem;
            /* 内边距 */
            display: flex;
            /* 弹性布局 */
            justify-content: space-between;
            /* 元素之间留有空间 */
            align-items: center;
            /* 垂直居中 */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            /* 阴影 */
            position: relative;
            /* 相对定位 */
            overflow: hidden;
            /* 溢出隐藏 */
        }
        
        header::before {
            /* 页头装饰效果 */
            content: '';
            /* 内容 */
            position: absolute;
            /* 绝对定位 */
            top: -50%;
            /* 顶部位置 */
            left: -50%;
            /* 左侧位置 */
            width: 200%;
            /* 宽度 */
            height: 200%;
            /* 高度 */
            background: linear-gradient(to right, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            /* 渐变背景 */
            transform: rotate(30deg);
            /* 旋转 */
            z-index: 1;
            /* 层级 */
        }
        
        header > * {
            /* 页头内所有元素 */
            position: relative;
            /* 相对定位 */
            z-index: 2;
            /* 层级 */
        }
        
        h1 {
            /* 一级标题样式 */
            font-size: 1.8rem;
            /* 字体大小 */
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
            /* 文字阴影 */
        }
        
        .user-info {
            /* 用户信息样式 */
            display: flex;
            /* 弹性布局 */
            align-items: center;
            /* 垂直居中 */
        }
        
        .user-info span {
            /* 用户信息文本样式 */
            margin-right: 15px;
            /* 右外边距 */
        }
        
        .logout-btn {
            /* 登出按钮样式 */
            background-color: transparent;
            /* 透明背景 */
            color: white;
            /* 文本颜色 */
            border: 1px solid white;
            /* 边框 */
            padding: 5px 10px;
            /* 内边距 */
            border-radius: 4px;
            /* 边框圆角 */
            cursor: pointer;
            /* 鼠标指针 */
        }
        
        .logout-btn:hover {
            /* 鼠标悬停登出按钮样式 */
            background-color: rgba(255, 255, 255, 0.2);
            /* 半透明白色背景 */
        }
        
        .dashboard {
            /* 仪表盘样式 */
            display: flex;
            /* 弹性布局 */
            margin-top: 20px;
            /* 上外边距 */
        }
        
        .sidebar {
            /* 侧边栏样式 */
            width: 220px;
            /* 宽度 */
            background: linear-gradient(180deg, #ffffff, #f8f9fa);
            /* 渐变背景 */
            border-radius: 10px;
            /* 边框圆角 */
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            /* 阴影 */
            padding: 25px;
            /* 内边距 */
            margin-right: 25px;
            /* 右外边距 */
            transition: all 0.3s ease;
            /* 过渡效果 */
        }
        
        .menu-item {
            /* 菜单项样式 */
            padding: 15px;
            /* 内边距 */
            margin-bottom: 8px;
            /* 下外边距 */
            background-color: #f8f9fb;
            /* 背景颜色 */
            border-radius: 8px;
            /* 边框圆角 */
            cursor: pointer;
            /* 鼠标指针 */
            transition: all 0.2s;
            /* 过渡效果 */
            border-left: 3px solid transparent;
            /* 左侧边框 */
            font-weight: 500;
            /* 字体粗细 */
        }
        
        .menu-item:hover {
            /* 菜单项悬停样式 */
            background-color: #f0f3f8;
            /* 背景颜色 */
            transform: translateX(5px);
            /* X轴位移 */
        }
        
        .menu-item.active {
            /* 活动菜单项样式 */
            background-color: #e8f5e9;
            /* 背景颜色 */
            color: #4caf50;
            /* 文本颜色 */
            border-left: 3px solid #4caf50;
            /* 左侧边框 */
            font-weight: bold;
            /* 字体粗细 */
        }
        
        .content {
            /* 内容区域样式 */
            flex: 1;
            /* 弹性比例 */
            background-color: white;
            /* 背景颜色 */
            border-radius: 10px;
            /* 边框圆角 */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            /* 阴影 */
            padding: 20px;
            /* 内边距 */
        }
        
        .section {
            /* 区块样式 */
            display: none;
            /* 默认隐藏 */
        }
        
        .section.active {
            /* 活动区块样式 */
            display: block;
            /* 显示为块级元素 */
        }
        
        h2 {
            /* 二级标题样式 */
            margin-bottom: 20px;
            /* 下外边距 */
            color: #333;
            /* 文本颜色 */
            border-bottom: 1px solid #eee;
            /* 底部边框 */
            padding-bottom: 10px;
            /* 下内边距 */
        }
        
        .search-bar {
            /* 搜索栏样式 */
            margin-bottom: 20px;
            /* 下外边距 */
            display: flex;
            /* 弹性布局 */
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 5px;
            background: rgba(255,255,255,0.9);
            transition: all 0.3s ease;
            animation: floatIn 0.8s ease-out;
        }
        
        .search-bar:hover {
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .search-bar input {
            /* 搜索输入框样式 */
            flex: 3;
            /* 增大输入框比例 */
            padding: 12px 15px;
            /* 内边距 */
            border: 1px solid #e0e0e0;
            /* 边框 */
            border-radius: 6px;
            /* 边框圆角 */
            font-size: 15px;
            /* 字体大小 */
            transition: all 0.3s ease;
            /* 过渡效果 */
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
            /* 内阴影 */
        }
        
        .search-bar input:focus {
            /* 输入框聚焦样式 */
            border-color: #4caf50;
            /* 边框颜色 */
            box-shadow: inset 0 1px 3px rgba(76, 175, 80, 0.2);
            /* 内阴影 */
            outline: none;
            /* 移除轮廓 */
        }
        
        .search-bar button {
            /* 搜索按钮样式 */
            flex: 1;
            /* 减小按钮比例 */
            padding: 12px 15px;
            /* 内边距 */
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            /* 渐变背景 */
            color: white;
            /* 文本颜色 */
            border: none;
            /* 无边框 */
            border-radius: 6px;
            /* 边框圆角 */
            margin-left: 10px;
            /* 左外边距 */
            cursor: pointer;
            /* 鼠标指针 */
            transition: all 0.3s ease;
            /* 过渡效果 */
            font-weight: bold;
            /* 字体粗细 */
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            /* 阴影效果 */
            max-width: 120px;
            /* 限制最大宽度 */
        }
        
        .search-bar button:hover {
            /* 搜索按钮悬停样式 */
            background: linear-gradient(135deg, #45a049 0%, #3d8b3d 100%);
            /* 悬停渐变背景 */
            transform: translateY(-2px);
            /* 上移效果 */
            box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3);
            /* 增强阴影 */
            animation: pulse 1.5s infinite alternate;
            /* 脉冲动画 */
        }
        
        .btn {
            /* 按钮通用样式 */
            padding: 8px 15px;
            /* 内边距 */
            border: none;
            /* 无边框 */
            border-radius: 4px;
            /* 边框圆角 */
            cursor: pointer;
            /* 鼠标指针 */
            margin-right: 5px;
            /* 右外边距 */
        }
        
        .btn-primary {
            /* 主要按钮样式 */
            background-color: #4caf50;
            /* 背景颜色 */
            color: white;
            /* 文本颜色 */
        }
        
        .btn-primary:hover {
            /* 主要按钮悬停样式 */
            background-color: #45a049;
            /* 背景颜色 */
        }
        
        .btn-danger {
            /* 危险按钮样式 */
            background-color: #f44336;
            /* 背景颜色 */
            color: white;
            /* 文本颜色 */
        }
        
        .btn-danger:hover {
            /* 危险按钮悬停样式 */
            background-color: #d32f2f;
            /* 背景颜色 */
        }
        
        .btn-secondary {
            /* 次要按钮样式 */
            background-color: #f1f1f1;
            /* 背景颜色 */
            color: #333;
            /* 文本颜色 */
        }
        
        .btn-secondary:hover {
            /* 次要按钮悬停样式 */
            background-color: #e0e0e0;
            /* 背景颜色 */
        }
        
        table {
            /* 表格样式 */
            width: 100%;
            /* 宽度 */
            border-collapse: collapse;
            /* 边框合并 */
            margin: 20px 0;
            /* 外边距 */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            /* 阴影 */
            border-radius: 10px;
            /* 边框圆角 */
            overflow: hidden;
            /* 溢出隐藏 */
        }
        
        thead {
            /* 表头样式 */
            background-color: #f5f7fa;
            /* 背景颜色 */
        }
        
        th {
            /* 表头单元格样式 */
            padding: 15px;
            /* 内边距 */
            text-align: left;
            /* 文本左对齐 */
            font-weight: bold;
            /* 字体粗细 */
            color: #5a6577;
            /* 文本颜色 */
            border-bottom: 2px solid #eaedf1;
            /* 底部边框 */
        }
        
        td {
            /* 表格单元格样式 */
            padding: 12px 15px;
            /* 内边距 */
            border-bottom: 1px solid #eaedf1;
            /* 底部边框 */
        }
        
        tbody tr {
            /* 表格行样式 */
            transition: all 0.2s;
            /* 过渡效果 */
        }
        
        tbody tr:hover {
            /* 表格行悬停样式 */
            background-color: #f8f9fb;
            /* 背景颜色 */
        }
    </style>
</head>
<body>
    <!-- 页面主体 -->
    <header>
        <!-- 页头 -->
        <h1>仓库管理系统 - 管理员面板</h1>
        <!-- 标题 -->
        <div id="system-time" class="system-time"></div>
        <!-- 系统时间显示 -->
        <div class="user-info">
            <!-- 用户信息 -->
            <span id="username">加载中...</span>
            <!-- 用户名 -->
            <button class="logout-btn" onclick="logout()">退出登录</button>
            <!-- 退出登录按钮 -->
        </div>
    </header>
    
    <div class="container">
        <!-- 主容器 -->
        <div id="messageContainer" class="message"></div>
        <!-- 消息容器 -->
        
        <div class="dashboard">
            <!-- 库存预警部分 -->
            <section id="warnings" class="section">
                <h2>库存预警</h2>
                <div class="warning-controls">
                    <div class="form-group">
                        <label for="warningThreshold">预警阈值：</label>
                        <input type="number" id="warningThreshold" min="1" value="10" class="form-control">
                        <button onclick="updateWarningThreshold()" class="btn">更新阈值</button>
                    </div>
                </div>
                <div id="warningsList" class="warnings-list">
                    <!-- 预警项目将在这里动态加载 -->
                </div>
            </section>
            <!-- 仪表盘 -->
            <div class="sidebar">
                <!-- 侧边栏 -->
                <div class="menu-item active" onclick="showSection('inventory')">库存管理</div>
                <!-- 库存管理菜单项 -->
                <div class="menu-item" onclick="showSection('users')">用户管理</div>
                <!-- 用户管理菜单项 -->
                <div class="menu-item" onclick="showSection('requests')">申请管理</div>
                <!-- 申请管理菜单项 -->
                <div class="menu-item warning-link" onclick="showSection('warnings'); loadWarnings()">库存预警<span id="warning-badge" class="warning-badge">0</span></div>
                <!-- 库存预警菜单项 -->
            </div>
            
            <div class="content">
                <!-- 内容区域 -->
                <div id="inventory" class="section active">
                    <!-- 库存管理区块 -->
                    <h2>库存管理</h2>
                    <!-- 标题 -->
                    
                    <div class="search-bar">
                        <!-- 搜索栏 -->
                        <input type="text" id="inventorySearch" placeholder="输入关键词搜索...">
                        <!-- 搜索输入框 -->
                        <button onclick="searchInventory()">搜索</button>
                        <!-- 搜索按钮 -->
                    </div>
                    
                    <button class="btn btn-primary" onclick="showAddItemModal()">添加物品</button>
                    <!-- 添加物品按钮 -->
                    
                    <div id="inventoryTable">
                        <!-- 库存表格容器 -->
                        <table>
                            <!-- 表格 -->
                            <thead>
                                <!-- 表头 -->
                                <tr>
                                    <!-- 表头行 -->
                                    <th>ID</th>
                                    <!-- ID列 -->
                                    <th>名称</th>
                                    <!-- 名称列 -->
                                    <th>描述</th>
                                    <!-- 描述列 -->
                                    <th>数量</th>
                                    <!-- 数量列 -->
                                    <th>价格</th>
                                    <!-- 价格列 -->
                                    <th>分类</th>
                                    <!-- 分类列 -->
                                    <th>操作</th>
                                    <!-- 操作列 -->
                                </tr>
                            </thead>
                            <tbody id="inventoryBody">
                                <!-- 表格主体 -->
                                <tr>
                                    <!-- 加载提示行 -->
                                    <td colspan="7" align="center">加载库存数据...</td>
                                    <!-- 跨列单元格 -->
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div id="users" class="section">
                    <!-- 用户管理区块 -->
                    <h2>用户管理</h2>
                    <!-- 标题 -->
                    
                    <div class="search-bar">
                        <!-- 搜索栏 -->
                        <input type="text" id="usersSearch" placeholder="输入用户名搜索...">
                        <!-- 搜索输入框 -->
                        <button onclick="searchUsers()">搜索</button>
                        <!-- 搜索按钮 -->
                    </div>
                    
                    <button class="btn btn-primary" onclick="showAddUserModal()">添加用户</button>
                    <!-- 添加用户按钮 -->
                    
                    <div id="usersTable">
                        <!-- 用户表格容器 -->
                        <table>
                            <!-- 表格 -->
                            <thead>
                                <!-- 表头 -->
                                <tr>
                                    <!-- 表头行 -->
                                    <th>ID</th>
                                    <!-- ID列 -->
                                    <th>用户名</th>
                                    <!-- 用户名列 -->
                                    <th>角色</th>
                                    <!-- 角色列 -->
                                    <th>注册时间</th>
                                    <!-- 注册时间列 -->
                                    <th>操作</th>
                                    <!-- 操作列 -->
                                </tr>
                            </thead>
                            <tbody id="usersBody">
                                <!-- 表格主体 -->
                                <tr>
                                    <!-- 加载提示行 -->
                                    <td colspan="5" align="center">加载用户数据...</td>
                                    <!-- 跨列单元格 -->
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- 申请管理区块 -->
                <div id="requests" class="section">
                    <h2>申请管理</h2>
                    <button class="btn btn-secondary" onclick="loadAllRequests()">刷新</button>
                    <div id="requestsTable">
                        <table>
                            <thead>
                                <tr><th>申请ID</th><th>用户ID</th><th>物品ID</th><th>数量</th><th>日期</th><th>状态</th><th>操作</th></tr>
                            </thead>
                            <tbody id="requestsBody">
                                <tr><td colspan="7" align="center">加载申请记录...</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // JavaScript代码部分
        // 页面加载完成后执行
        // 更新系统时间显示
        function updateSystemTime() {
            const now = new Date();
            const options = { 
                year: 'numeric', 
                month: '2-digit', 
                day: '2-digit',
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit',
                hour12: false
            };
            const timeString = now.toLocaleString('zh-CN', options);
            document.getElementById('system-time').textContent = '系统时间：' + timeString;
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // 检查用户是否已登录
            checkLogin(); // 检查登录状态
            
            // 加载库存数据
            loadInventory(); // 加载库存数据
            
            // 加载用户数据
            loadUsers(); // 加载用户数据
            
            // 初始化系统时间并设置定时更新
            updateSystemTime();
            // 每秒更新一次时间
            setInterval(updateSystemTime, 1000);
            
            // 加载库存预警数据
            checkWarnings();
            // 每分钟检查一次库存预警
            setInterval(checkWarnings, 60000);
        });
        
        // 检查用户是否已登录
        function checkLogin() {
            // 从本地存储获取用户信息
            const userJson = localStorage.getItem('user'); // 获取用户信息
            
            if (!userJson) {
                // 如果没有用户信息，重定向到登录页面
                window.location.href = 'index.html'; // 跳转到登录页面
                return; // 结束函数
            }
            
            // 解析用户信息
            const user = JSON.parse(userJson); // 解析用户数据
            
            // 如果不是管理员，重定向到用户面板
            if (user.role !== 'admin') {
                // 如果不是管理员
                window.location.href = 'user_dashboard.html'; // 跳转到用户面板
                return; // 结束函数
            }
            
            // 显示用户名
            document.getElementById('username').textContent = user.username; // 显示用户名
        }
        
        // 显示指定区块
        function showSection(sectionId) {
            // 隐藏所有区块
            const sections = document.querySelectorAll('.section'); // 获取所有区块
            sections.forEach(section => {
                section.classList.remove('active'); // 移除活动状态
                section.style.display = 'none'; // 隐藏区块
            });
            
            // 取消所有菜单项的选中状态
            const menuItems = document.querySelectorAll('.menu-item'); // 获取所有菜单项
            menuItems.forEach(item => item.classList.remove('active')); // 移除活动状态
            
            // 显示选中的区块
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active'); // 添加活动状态
                targetSection.style.display = 'block'; // 显示区块
            }
            
            // 高亮对应菜单项
            const clickedItem = document.querySelector(`.sidebar .menu-item[onclick*="${sectionId}"]`);
            if (clickedItem) clickedItem.classList.add('active');
            // 进入申请管理时加载数据
            if (sectionId === 'requests') loadAllRequests();
        }
        
        // 加载库存数据
        function loadInventory() {
            // 使用模拟API获取库存数据
            setTimeout(() => {
                // 模拟网络延迟
                const data = mockGetInventory(); // 调用模拟函数获取数据
                
                if (data.success) {
                    // 清空表格
                    const tableBody = document.getElementById('inventoryBody');
                    tableBody.innerHTML = '';
                    
                    // 填充表格
                    if (data.items.length === 0) {
                        tableBody.innerHTML = '<tr><td colspan="7" align="center">没有找到物品</td></tr>';
                    } else {
                        data.items.forEach(item => {
                            // 表格行
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${item.id}</td>
                                <td>${item.name}</td>
                                <td>${item.description || '-'}</td>
                                <td>${item.quantity}</td>
                                <td>${item.price ? '¥' + parseFloat(item.price).toFixed(2) : '-'}</td>
                                <td>${item.category || '-'}</td>
                                <td>
                                    <button class="btn btn-primary" onclick="editItem(${item.id})">编辑</button>
                                    <button class="btn btn-danger" onclick="deleteItem(${item.id})">删除</button>
                                </td>
                            `;
                            tableBody.appendChild(row);
                        });
                    }
                } else {
                    // 显示错误消息
                    alert(data.message || '加载库存失败，请重试');
                }
            }, 500); // 延时500毫秒模拟网络请求
        }
        
        // 搜索库存
        function searchInventory() {
            // 获取搜索关键词
            const keyword = document.getElementById('inventorySearch').value.trim();
            
            // 使用模拟API搜索库存
            setTimeout(() => {
                // 模拟网络延迟
                const data = mockSearchInventory(keyword); // 调用模拟搜索函数
                
                // 清空表格
                const tableBody = document.getElementById('inventoryBody');
                tableBody.innerHTML = '';
                
                // 填充表格
                if (data.items.length === 0) {
                    tableBody.innerHTML = '<tr><td colspan="7" align="center">没有找到匹配的物品</td></tr>';
                } else {
                    data.items.forEach(item => {
                        // 表格行
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${item.id}</td>
                            <td>${item.name}</td>
                            <td>${item.description || '-'}</td>
                            <td>${item.quantity}</td>
                            <td>${item.price ? '¥' + parseFloat(item.price).toFixed(2) : '-'}</td>
                            <td>${item.category || '-'}</td>
                            <td>
                                <button class="btn btn-primary" onclick="editItem(${item.id})">编辑</button>
                                <button class="btn btn-danger" onclick="deleteItem(${item.id})">删除</button>
                            </td>
                        `;
                        tableBody.appendChild(row);
                    });
                }
            }, 500); // 延时500毫秒模拟网络请求
        }
        
        // 添加物品
        function showAddItemModal() {
            // 简单实现：使用prompt获取输入
            const name = prompt('物品名称:');
            if (!name) return; // 如果取消或未输入名称，则返回
            
            const description = prompt('物品描述:');
            const quantity = parseInt(prompt('物品数量:') || '0');
            const price = parseFloat(prompt('物品价格:') || '0');
            const category = prompt('物品分类:');
            
            // 调用模拟API添加物品
            const result = mockAddItem({
                name,
                description,
                quantity,
                price,
                category
            });
            
            if (result.success) {
                alert('物品添加成功！');
                loadInventory(); // 重新加载库存数据
            } else {
                alert(result.message || '添加物品失败，请重试');
            }
        }
        
        // 编辑物品
        function editItem(id) {
            // 简单实现：使用prompt获取输入
            // 查找物品
            const item = mockGetInventory().items.find(item => item.id === id);
            if (!item) {
                alert('物品不存在！');
                return;
            }
            
            const name = prompt('物品名称:', item.name);
            if (!name) return; // 如果取消或未输入名称，则返回
            
            const description = prompt('物品描述:', item.description || '');
            const quantity = parseInt(prompt('物品数量:', item.quantity) || '0');
            const price = parseFloat(prompt('物品价格:', item.price) || '0');
            const category = prompt('物品分类:', item.category || '');
            
            // 调用模拟API更新物品
            const result = mockUpdateItem(id, {
                name,
                description,
                quantity,
                price,
                category
            });
            
            if (result.success) {
                alert('物品更新成功！');
                loadInventory(); // 重新加载库存数据
            } else {
                alert(result.message || '更新物品失败，请重试');
            }
        }
        
        // 删除物品
        function deleteItem(id) {
            // 确认删除
            if (!confirm('确定要删除这个物品吗？此操作不可撤销！')) {
                return; // 如果取消，则返回
            }
            
            // 调用模拟API删除物品
            const result = mockDeleteItem(id);
            
            if (result.success) {
                alert('物品删除成功！');
                loadInventory(); // 重新加载库存数据
            } else {
                alert(result.message || '删除物品失败，请重试');
            }
        }
        
        // 加载用户数据
        function loadUsers() {
            // 直接访问模拟用户数据
            setTimeout(() => {
                // 模拟网络延迟
                const tableBody = document.getElementById('usersBody');
                tableBody.innerHTML = '';
                
                if (users.length === 0) {
                    tableBody.innerHTML = '<tr><td colspan="5" align="center">没有找到用户</td></tr>';
                } else {
                    users.forEach(user => {
                        // 不显示密码
                        const { password, ...userInfo } = user;
                        
                        // 表格行
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${userInfo.id}</td>
                            <td>${userInfo.username}</td>
                            <td>${userInfo.role === 'admin' ? '管理员' : '普通用户'}</td>
                            <td>${new Date(userInfo.created_at).toLocaleString()}</td>
                            <td>
                                <button class="btn btn-danger" onclick="deleteUser(${userInfo.id})">删除</button>
                            </td>
                        `;
                        tableBody.appendChild(row);
                    });
                }
            }, 500); // 延时500毫秒模拟网络请求
        }
        
        // 加载库存预警数据
        function loadWarnings() {
            // 获取当前预警阈值
            const thresholdResult = mockGetWarningThreshold();
            if (thresholdResult.success) {
                document.getElementById('warningThreshold').value = thresholdResult.threshold;
            }
            
            // 获取预警数据
            const threshold = parseInt(document.getElementById('warningThreshold').value);
            const result = mockGetInventoryWarnings(threshold);
            
            const warningsList = document.getElementById('warningsList');
            warningsList.innerHTML = '';
            
            if (result.success && result.items.length > 0) {
                result.items.forEach(item => {
                    const warningItem = document.createElement('div');
                    warningItem.className = 'warning-item';
                    warningItem.innerHTML = `
                        <h3>${item.name}</h3>
                        <p>当前库存: <strong>${item.quantity}</strong> (低于预警阈值 ${threshold})</p>
                        <p>分类: ${item.category}</p>
                        <button class="btn" onclick="addInventory(${item.id})">补充库存</button>
                    `;
                    warningsList.appendChild(warningItem);
                });
            } else {
                warningsList.innerHTML = '<p>当前没有低于阈值的库存物品</p>';
            }
        }
        
        // 检查库存预警状态并更新导航栏标记
        function checkWarnings() {
            const threshold = parseInt(localStorage.getItem('warningThreshold') || '10');
            const result = mockGetInventoryWarnings(threshold);
            
            const warningBadge = document.getElementById('warning-badge');
            if (result.success && result.items.length > 0) {
                warningBadge.textContent = result.items.length;
                warningBadge.style.display = 'inline-flex';
            } else {
                warningBadge.textContent = '0';
                warningBadge.style.display = 'none';
            }
        }
        
        // 更新预警阈值
        function updateWarningThreshold() {
            const threshold = parseInt(document.getElementById('warningThreshold').value);
            if (isNaN(threshold) || threshold < 1) {
                alert('请输入有效的预警阈值（大于等于1）');
                return;
            }
            
            const result = mockUpdateWarningThreshold(threshold);
            if (result.success) {
                alert('预警阈值更新成功！');
                loadWarnings();
                checkWarnings();
            } else {
                alert('更新预警阈值失败，请重试');
            }
        }
        
        // 补充库存
        function addInventory(itemId) {
            // 获取物品信息
            const item = inventory.find(i => i.id === itemId);
            if (!item) {
                alert('物品不存在');
                return;
            }
            
            // 弹出输入框
            const quantity = parseInt(prompt(`请输入要补充的 ${item.name} 数量:`, '10'));
            if (isNaN(quantity) || quantity <= 0) {
                alert('请输入有效的数量');
                return;
            }
            
            // 更新库存
            const newQuantity = item.quantity + quantity;
            const result = mockUpdateItem(itemId, { quantity: newQuantity });
            
            if (result.success) {
                alert(`成功补充 ${quantity} 个 ${item.name}，当前库存: ${newQuantity}`);
                loadWarnings();
                checkWarnings();
                // 如果当前在库存页面，也更新库存显示
                if (document.getElementById('inventory').classList.contains('active')) {
                    loadInventory();
                }
            } else {
                alert('补充库存失败，请重试');
            }
        }
        
        // 搜索用户
        function searchUsers() {
            // 获取搜索关键词
            const keyword = document.getElementById('usersSearch').value.trim().toLowerCase();
            
            // 使用模拟用户数据搜索
            setTimeout(() => {
                // 模拟网络延迟
                const tableBody = document.getElementById('usersBody');
                tableBody.innerHTML = '';
                
                // 过滤用户
                const filteredUsers = keyword ? 
                    users.filter(user => user.username.toLowerCase().includes(keyword) || 
                                          user.role.toLowerCase().includes(keyword)) :
                    users;
                
                if (filteredUsers.length === 0) {
                    tableBody.innerHTML = '<tr><td colspan="5" align="center">没有找到匹配的用户</td></tr>';
                } else {
                    filteredUsers.forEach(user => {
                        // 不显示密码
                        const { password, ...userInfo } = user;
                        
                        // 表格行
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${userInfo.id}</td>
                            <td>${userInfo.username}</td>
                            <td>${userInfo.role === 'admin' ? '管理员' : '普通用户'}</td>
                            <td>${new Date(userInfo.created_at).toLocaleString()}</td>
                            <td>
                                <button class="btn btn-danger" onclick="deleteUser(${userInfo.id})">删除</button>
                            </td>
                        `;
                        tableBody.appendChild(row);
                    });
                }
            }, 500); // 延时500毫秒模拟网络请求
        }
        
        // 添加用户
        function showAddUserModal() {
            // 简单实现：使用prompt获取输入
            const username = prompt('用户名:');
            if (!username) return; // 如果取消或未输入用户名，则返回
            
            const password = prompt('密码:');
            if (!password) return; // 如果取消或未输入密码，则返回
            
            const role = confirm('是管理员用户吗？') ? 'admin' : 'user';
            
            // 调用模拟API注册用户
            const result = mockRegister(username, password, role);
            
            if (result.success) {
                alert('用户添加成功！');
                loadUsers(); // 重新加载用户数据
            } else {
                alert(result.message || '添加用户失败，请重试');
            }
        }
        
        // 删除用户
        function deleteUser(userId) {
            // 确认删除
            if (!confirm('确定要删除这个用户吗？此操作不可撤销！')) {
                return; // 如果取消，则返回
            }
            
            // 查找用户索引
            const index = users.findIndex(user => user.id === userId);
            if (index === -1) {
                alert('用户不存在！');
                return;
            }
            
            // 如果是当前登录的用户，不允许删除
            const currentUser = JSON.parse(localStorage.getItem('user'));
            if (currentUser && currentUser.id === userId) {
                alert('不能删除当前登录的用户！');
                return;
            }
            
            // 从数组中删除用户
            users.splice(index, 1);
            
            alert('用户删除成功！');
            loadUsers(); // 重新加载用户数据
        }
        
        // 退出登录
        function logout() {
            // 清除本地存储中的用户信息
            localStorage.removeItem('user'); // 移除用户信息
            
            // 跳转到登录页面
            window.location.href = 'index.html'; // 跳转到登录页面
        }
        
        // 加载所有申请记录
        function loadAllRequests() {
            // 禁用刷新按钮，防止重复点击
            const refreshBtn = document.querySelector('button[onclick="loadAllRequests()"]');
            if (refreshBtn) {
                refreshBtn.disabled = true;
                refreshBtn.textContent = '加载中...';
            }
            
            setTimeout(() => {
                const res = mockGetAllRequests(); // 获取所有申请记录
                const tbody = document.getElementById('requestsBody');
                tbody.innerHTML = '';
                if (res.requests.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="7" align="center">暂无申请记录</td></tr>';
                } else {
                    res.requests.forEach(r => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${r.id}</td>
                            <td>${r.userId}</td>
                            <td>${r.itemId}</td>
                            <td>${r.quantity}</td>
                            <td>${r.date}</td>
                            <td>${r.status}</td>
                            <td>
                                <button class="btn btn-primary" onclick="approveRequest('${r.id}')">通过</button>
                                <button class="btn btn-danger" onclick="rejectRequest('${r.id}')">拒绝</button>
                            </td>`;
                        tbody.appendChild(row);
                    });
                }
                
                // 恢复刷新按钮状态
                if (refreshBtn) {
                    refreshBtn.disabled = false;
                    refreshBtn.textContent = '刷新';
                }
            }, 500);
        }
        
        // 审批通过
        function approveRequest(id) {
            const res = mockUpdateRequestStatus(id, '已通过');
            alert(res.message);
            loadAllRequests();
        }
        
        // 审批拒绝
        function rejectRequest(id) {
            const res = mockUpdateRequestStatus(id, '已拒绝');
            alert(res.message);
            loadAllRequests();
        }
    </script>
</body>
</html>