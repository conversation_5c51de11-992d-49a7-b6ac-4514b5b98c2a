<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>业务管理员仪表板 - 智能仓库管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: bold;
            color: #2d3748 !important;
        }
        
        .main-content {
            margin-top: 20px;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
            margin-bottom: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .card-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
            padding: 15px 20px;
        }
        
        .stat-card {
            text-align: center;
            padding: 20px;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #4facfe;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .badge {
            border-radius: 20px;
            padding: 5px 10px;
        }
        
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        
        .form-control {
            border-radius: 10px;
            border: 1px solid #e2e8f0;
            padding: 12px 15px;
        }
        
        .form-control:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .nav-pills .nav-link {
            border-radius: 10px;
            margin-right: 10px;
            transition: all 0.3s ease;
        }
        
        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .quick-action {
            text-align: center;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .quick-action:hover {
            background: rgba(79, 172, 254, 0.1);
        }
        
        .quick-action i {
            font-size: 2rem;
            color: #4facfe;
            margin-bottom: 10px;
        }
        
        .request-item {
            padding: 15px;
            border-left: 4px solid #4facfe;
            margin-bottom: 10px;
            background: rgba(79, 172, 254, 0.05);
            border-radius: 0 10px 10px 0;
        }
        
        .request-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .request-user {
            font-weight: 600;
            color: #4facfe;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-briefcase me-2"></i>业务管理员控制台
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user-tie me-1"></i>
                    欢迎，<span id="username">业务管理员</span>
                </span>
                <button class="btn btn-outline-danger btn-sm" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-1"></i>退出登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid main-content" style="margin-top: 80px;">
        <!-- 业务统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-number" id="totalItems">0</div>
                    <div class="stat-label">管理物品数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-number" id="lowStockItems">0</div>
                    <div class="stat-label">库存不足物品</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-number" id="pendingRequests">0</div>
                    <div class="stat-label">待审批申请</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-number" id="todayTransactions">0</div>
                    <div class="stat-label">今日交易数</div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>快速操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="quick-action" onclick="showAddItemModal()">
                                    <i class="fas fa-plus-circle"></i>
                                    <div>添加物品</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="quick-action" onclick="showInventoryModal()">
                                    <i class="fas fa-boxes"></i>
                                    <div>库存管理</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="quick-action" onclick="showReportsModal()">
                                    <i class="fas fa-chart-bar"></i>
                                    <div>生成报表</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="quick-action" onclick="showSuppliersModal()">
                                    <i class="fas fa-truck"></i>
                                    <div>供应商管理</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能导航 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>业务管理功能</h5>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="pills-requests-tab" data-bs-toggle="pill" data-bs-target="#pills-requests" type="button" role="tab">
                                    <i class="fas fa-clipboard-list me-1"></i>申请审批
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pills-inventory-tab" data-bs-toggle="pill" data-bs-target="#pills-inventory" type="button" role="tab">
                                    <i class="fas fa-warehouse me-1"></i>库存监控
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pills-items-tab" data-bs-toggle="pill" data-bs-target="#pills-items" type="button" role="tab">
                                    <i class="fas fa-cube me-1"></i>物品管理
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pills-reports-tab" data-bs-toggle="pill" data-bs-target="#pills-reports" type="button" role="tab">
                                    <i class="fas fa-chart-line me-1"></i>统计报表
                                </button>
                            </li>
                        </ul>
                        
                        <div class="tab-content" id="pills-tabContent">
                            <!-- 申请审批标签页 -->
                            <div class="tab-pane fade show active" id="pills-requests" role="tabpanel">
                                <h6>待审批申请</h6>
                                <div id="pendingRequestsList">
                                    <!-- 申请数据将通过JavaScript动态加载 -->
                                </div>
                            </div>
                            
                            <!-- 库存监控标签页 -->
                            <div class="tab-pane fade" id="pills-inventory" role="tabpanel">
                                <h6>库存状态监控</h6>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>物品名称</th>
                                                <th>当前库存</th>
                                                <th>最低库存</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="inventoryTableBody">
                                            <!-- 库存数据将通过JavaScript动态加载 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- 物品管理标签页 -->
                            <div class="tab-pane fade" id="pills-items" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>物品管理</h6>
                                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addItemModal">
                                        <i class="fas fa-plus me-1"></i>添加物品
                                    </button>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>物品名称</th>
                                                <th>分类</th>
                                                <th>单价</th>
                                                <th>供应商</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="itemsTableBody">
                                            <!-- 物品数据将通过JavaScript动态加载 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- 统计报表标签页 -->
                            <div class="tab-pane fade" id="pills-reports" role="tabpanel">
                                <h6>业务统计报表</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title">库存周转报表</h6>
                                                <p class="card-text">分析物品库存周转情况</p>
                                                <button class="btn btn-primary">生成报表</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title">出入库统计</h6>
                                                <p class="card-text">统计指定时间段的出入库情况</p>
                                                <button class="btn btn-primary">生成报表</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title">供应商绩效</h6>
                                                <p class="card-text">分析供应商供货质量和效率</p>
                                                <button class="btn btn-primary">生成报表</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title">成本分析</h6>
                                                <p class="card-text">分析库存成本和采购成本</p>
                                                <button class="btn btn-primary">生成报表</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加物品模态框 -->
    <div class="modal fade" id="addItemModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加新物品</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addItemForm">
                        <div class="mb-3">
                            <label class="form-label">物品名称</label>
                            <input type="text" class="form-control" id="itemName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">物品分类</label>
                            <select class="form-control" id="itemCategory" required>
                                <option value="">请选择分类</option>
                                <option value="电子设备">电子设备</option>
                                <option value="办公用品">办公用品</option>
                                <option value="办公家具">办公家具</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">单价</label>
                            <input type="number" class="form-control" id="itemPrice" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">供应商</label>
                            <input type="text" class="form-control" id="itemSupplier" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">描述</label>
                            <textarea class="form-control" id="itemDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addItem()">添加物品</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../scripts/mock-api.js"></script>
    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查用户权限
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            if (!user.username || user.role !== 'business_admin') {
                alert('权限不足，请重新登录');
                window.location.href = 'index.html';
                return;
            }
            
            // 设置用户名
            document.getElementById('username').textContent = user.username;
            
            // 加载数据
            loadBusinessStats();
            loadPendingRequests();
            loadInventory();
            loadItems();
        });
        
        // 加载业务统计数据
        function loadBusinessStats() {
            // 模拟业务统计数据
            document.getElementById('totalItems').textContent = '156';
            document.getElementById('lowStockItems').textContent = '8';
            document.getElementById('pendingRequests').textContent = '12';
            document.getElementById('todayTransactions').textContent = '24';
        }
        
        // 加载待审批申请
        function loadPendingRequests() {
            const requests = mockGetRequests();
            const container = document.getElementById('pendingRequestsList');
            container.innerHTML = '';
            
            if (requests.success) {
                const pendingRequests = requests.requests.filter(req => req.status === '待审批');
                
                pendingRequests.forEach(request => {
                    const requestItem = document.createElement('div');
                    requestItem.className = 'request-item';
                    requestItem.innerHTML = `
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="request-time">${request.date}</div>
                                <div class="request-user">用户: ${request.user}</div>
                                <div>申请: ${request.item} x ${request.quantity}</div>
                                <div class="text-muted">用途: ${request.purpose}</div>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-success me-1" onclick="approveRequest(${request.id})">
                                    <i class="fas fa-check"></i> 批准
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="rejectRequest(${request.id})">
                                    <i class="fas fa-times"></i> 拒绝
                                </button>
                            </div>
                        </div>
                    `;
                    container.appendChild(requestItem);
                });
                
                if (pendingRequests.length === 0) {
                    container.innerHTML = '<div class="text-center text-muted">暂无待审批申请</div>';
                }
            }
        }
        
        // 加载库存数据
        function loadInventory() {
            const inventory = mockGetInventory();
            if (inventory.success) {
                const tbody = document.getElementById('inventoryTableBody');
                tbody.innerHTML = '';
                
                inventory.items.forEach(item => {
                    const row = document.createElement('tr');
                    const status = item.quantity <= 10 ? 
                        '<span class="badge bg-danger">库存不足</span>' : 
                        '<span class="badge bg-success">正常</span>';
                    
                    row.innerHTML = `
                        <td>${item.name}</td>
                        <td>${item.quantity}</td>
                        <td>10</td>
                        <td>${status}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="adjustStock(${item.id})">
                                <i class="fas fa-edit"></i> 调整
                            </button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }
        }
        
        // 加载物品数据
        function loadItems() {
            const inventory = mockGetInventory();
            if (inventory.success) {
                const tbody = document.getElementById('itemsTableBody');
                tbody.innerHTML = '';
                
                inventory.items.forEach(item => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${item.name}</td>
                        <td>${item.category || '未分类'}</td>
                        <td>¥${item.price || '0.00'}</td>
                        <td>${item.supplier || '未指定'}</td>
                        <td><span class="badge bg-success">正常</span></td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="editItem(${item.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteItem(${item.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }
        }
        
        // 批准申请
        function approveRequest(requestId) {
            if (confirm('确定要批准这个申请吗？')) {
                const result = mockApproveRequest(requestId);
                if (result.success) {
                    alert('申请已批准');
                    loadPendingRequests();
                    loadBusinessStats();
                } else {
                    alert(result.message);
                }
            }
        }
        
        // 拒绝申请
        function rejectRequest(requestId) {
            const reason = prompt('请输入拒绝原因:');
            if (reason) {
                const result = mockRejectRequest(requestId, reason);
                if (result.success) {
                    alert('申请已拒绝');
                    loadPendingRequests();
                    loadBusinessStats();
                } else {
                    alert(result.message);
                }
            }
        }
        
        // 添加物品
        function addItem() {
            const name = document.getElementById('itemName').value;
            const category = document.getElementById('itemCategory').value;
            const price = document.getElementById('itemPrice').value;
            const supplier = document.getElementById('itemSupplier').value;
            const description = document.getElementById('itemDescription').value;
            
            if (!name || !category || !price || !supplier) {
                alert('请填写所有必填字段');
                return;
            }
            
            // 模拟添加物品
            alert('物品添加成功');
            bootstrap.Modal.getInstance(document.getElementById('addItemModal')).hide();
            loadItems();
            loadBusinessStats();
            document.getElementById('addItemForm').reset();
        }
        
        // 调整库存
        function adjustStock(itemId) {
            const newQuantity = prompt('请输入新的库存数量:');
            if (newQuantity && !isNaN(newQuantity)) {
                alert('库存调整成功');
                loadInventory();
                loadBusinessStats();
            }
        }
        
        // 编辑物品
        function editItem(itemId) {
            alert('编辑功能开发中...');
        }
        
        // 删除物品
        function deleteItem(itemId) {
            if (confirm('确定要删除这个物品吗？')) {
                alert('物品删除成功');
                loadItems();
                loadBusinessStats();
            }
        }
        
        // 快速操作函数
        function showAddItemModal() {
            new bootstrap.Modal(document.getElementById('addItemModal')).show();
        }
        
        function showInventoryModal() {
            // 切换到库存监控标签
            document.getElementById('pills-inventory-tab').click();
        }
        
        function showReportsModal() {
            // 切换到统计报表标签
            document.getElementById('pills-reports-tab').click();
        }
        
        function showSuppliersModal() {
            alert('供应商管理功能开发中...');
        }
        
        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('user');
                window.location.href = 'index.html';
            }
        }
    </script>
</body>
</html>
