<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统管理员仪表板 - 智能仓库管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: bold;
            color: #4a5568 !important;
        }
        
        .main-content {
            margin-top: 20px;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
            margin-bottom: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
            padding: 15px 20px;
        }
        
        .stat-card {
            text-align: center;
            padding: 20px;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .badge {
            border-radius: 20px;
            padding: 5px 10px;
        }
        
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        
        .form-control {
            border-radius: 10px;
            border: 1px solid #e2e8f0;
            padding: 12px 15px;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .nav-pills .nav-link {
            border-radius: 10px;
            margin-right: 10px;
            transition: all 0.3s ease;
        }
        
        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .log-entry {
            padding: 10px;
            border-left: 4px solid #667eea;
            margin-bottom: 10px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 0 10px 10px 0;
        }
        
        .log-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .log-action {
            font-weight: 600;
            color: #667eea;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-cogs me-2"></i>系统管理员控制台
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user-shield me-1"></i>
                    欢迎，<span id="username">系统管理员</span>
                </span>
                <button class="btn btn-outline-danger btn-sm" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-1"></i>退出登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid main-content" style="margin-top: 80px;">
        <!-- 系统统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-number" id="totalUsers">0</div>
                    <div class="stat-label">总用户数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-number" id="totalItems">0</div>
                    <div class="stat-label">物品总数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-number" id="pendingRequests">0</div>
                    <div class="stat-label">待处理申请</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-number" id="systemUptime">0</div>
                    <div class="stat-label">系统运行时间</div>
                </div>
            </div>
        </div>

        <!-- 功能导航 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-tools me-2"></i>系统管理功能</h5>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="pills-users-tab" data-bs-toggle="pill" data-bs-target="#pills-users" type="button" role="tab">
                                    <i class="fas fa-users me-1"></i>用户管理
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pills-logs-tab" data-bs-toggle="pill" data-bs-target="#pills-logs" type="button" role="tab">
                                    <i class="fas fa-file-alt me-1"></i>系统日志
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pills-settings-tab" data-bs-toggle="pill" data-bs-target="#pills-settings" type="button" role="tab">
                                    <i class="fas fa-cog me-1"></i>系统设置
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pills-backup-tab" data-bs-toggle="pill" data-bs-target="#pills-backup" type="button" role="tab">
                                    <i class="fas fa-database me-1"></i>数据备份
                                </button>
                            </li>
                        </ul>
                        
                        <div class="tab-content" id="pills-tabContent">
                            <!-- 用户管理标签页 -->
                            <div class="tab-pane fade show active" id="pills-users" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>用户管理</h6>
                                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                        <i class="fas fa-plus me-1"></i>添加用户
                                    </button>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>用户名</th>
                                                <th>角色</th>
                                                <th>创建时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="usersTableBody">
                                            <!-- 用户数据将通过JavaScript动态加载 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- 系统日志标签页 -->
                            <div class="tab-pane fade" id="pills-logs" role="tabpanel">
                                <h6>系统操作日志</h6>
                                <div id="systemLogs">
                                    <!-- 日志数据将通过JavaScript动态加载 -->
                                </div>
                            </div>
                            
                            <!-- 系统设置标签页 -->
                            <div class="tab-pane fade" id="pills-settings" role="tabpanel">
                                <h6>系统配置</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">系统名称</label>
                                            <input type="text" class="form-control" value="智能仓库管理系统">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">会话超时时间（分钟）</label>
                                            <input type="number" class="form-control" value="30">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">最大登录尝试次数</label>
                                            <input type="number" class="form-control" value="5">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">密码最小长度</label>
                                            <input type="number" class="form-control" value="6">
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-primary">保存设置</button>
                            </div>
                            
                            <!-- 数据备份标签页 -->
                            <div class="tab-pane fade" id="pills-backup" role="tabpanel">
                                <h6>数据备份与恢复</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title">数据备份</h6>
                                                <p class="card-text">创建系统数据的完整备份</p>
                                                <button class="btn btn-primary">立即备份</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title">数据恢复</h6>
                                                <p class="card-text">从备份文件恢复系统数据</p>
                                                <input type="file" class="form-control mb-2" accept=".sql,.backup">
                                                <button class="btn btn-warning">恢复数据</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加用户模态框 -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加新用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" id="newUsername" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">密码</label>
                            <input type="password" class="form-control" id="newPassword" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">真实姓名</label>
                            <input type="text" class="form-control" id="newRealName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">邮箱</label>
                            <input type="email" class="form-control" id="newEmail">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">角色</label>
                            <select class="form-control" id="newUserRole" required>
                                <option value="user">普通用户</option>
                                <option value="business_admin">业务管理员</option>
                                <option value="system_admin">系统管理员</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addUser()">添加用户</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../scripts/api-client.js"></script>
    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查用户权限
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            if (!user.username || user.role !== 'system_admin') {
                alert('权限不足，请重新登录');
                window.location.href = 'index.html';
                return;
            }
            
            // 设置用户名
            document.getElementById('username').textContent = user.real_name || user.username;
            
            // 加载数据
            loadSystemStats();
            loadUsers();
            loadSystemLogs();
        });
        
        // 加载系统统计数据
        async function loadSystemStats() {
            try {
                const result = await apiClient.getSystemStats();
                if (result.success) {
                    // 使用动画效果更新数字
                    animateNumber('totalUsers', result.stats.totalUsers);
                    animateNumber('totalItems', result.stats.totalItems);
                    animateNumber('pendingRequests', result.stats.pendingRequests);
                    document.getElementById('systemUptime').textContent = result.stats.systemUptime;
                } else {
                    console.error('获取系统统计失败:', result.message);
                    showNotification('获取系统统计失败', 'error');
                }
            } catch (error) {
                console.error('加载系统统计失败:', error);
                showNotification('加载系统统计失败，请检查网络连接', 'error');
            }
        }

        // 数字动画效果
        function animateNumber(elementId, targetValue) {
            const element = document.getElementById(elementId);
            const currentValue = parseInt(element.textContent) || 0;
            const increment = targetValue > currentValue ? 1 : -1;
            const duration = 500; // 500ms
            const steps = Math.abs(targetValue - currentValue);
            const stepDuration = steps > 0 ? duration / steps : 0;

            if (steps === 0) return;

            let current = currentValue;
            const timer = setInterval(() => {
                current += increment;
                element.textContent = current;

                if (current === targetValue) {
                    clearInterval(timer);
                }
            }, stepDuration);
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '9999';
            notification.style.minWidth = '300px';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }
        
        // 加载用户列表
        async function loadUsers() {
            try {
                const result = await apiClient.getAllUsers();
                if (result.success) {
                    const tbody = document.getElementById('usersTableBody');
                    tbody.innerHTML = '';
                    
                    result.users.forEach(user => {
                        const row = document.createElement('tr');
                        
                        let roleText = '';
                        let roleBadge = '';
                        switch(user.role) {
                            case 'system_admin':
                                roleText = '系统管理员';
                                roleBadge = 'bg-danger';
                                break;
                            case 'business_admin':
                                roleText = '业务管理员';
                                roleBadge = 'bg-warning';
                                break;
                            case 'user':
                                roleText = '普通用户';
                                roleBadge = 'bg-info';
                                break;
                        }
                        
                        row.innerHTML = `
                            <td>${user.id}</td>
                            <td>${user.username}</td>
                            <td><span class="badge ${roleBadge}">${roleText}</span></td>
                            <td>${new Date(user.created_at).toLocaleDateString()}</td>
                            <td>
                                ${user.id !== 1 ? `
                                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editUser(${user.id})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.id})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                ` : '<span class="text-muted">系统账号</span>'}
                            </td>
                        `;
                        tbody.appendChild(row);
                    });
                }
            } catch (error) {
                console.error('加载用户列表失败:', error);
            }
        }
        
        // 加载系统日志
        async function loadSystemLogs() {
            try {
                const result = await apiClient.getSystemLogs();
                if (result.success) {
                    const logsContainer = document.getElementById('systemLogs');
                    logsContainer.innerHTML = '';
                    
                    result.logs.forEach(log => {
                        const logEntry = document.createElement('div');
                        logEntry.className = 'log-entry';
                        logEntry.innerHTML = `
                            <div class="log-time">${new Date(log.timestamp).toLocaleString()}</div>
                            <div class="log-action">${log.action}</div>
                            <div>用户: ${log.user} | ${log.details}</div>
                        `;
                        logsContainer.appendChild(logEntry);
                    });
                }
            } catch (error) {
                console.error('加载系统日志失败:', error);
            }
        }
        
        // 添加用户
        async function addUser() {
            const username = document.getElementById('newUsername').value;
            const password = document.getElementById('newPassword').value;
            const real_name = document.getElementById('newRealName').value;
            const email = document.getElementById('newEmail').value;
            const role = document.getElementById('newUserRole').value;
            
            if (!username || !password || !real_name) {
                alert('请填写所有必填字段');
                return;
            }
            
            try {
                const result = await apiClient.addUser({ username, password, real_name, email, role });
                if (result.success) {
                    alert('用户添加成功');
                    bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
                    loadUsers();
                    loadSystemStats();
                    document.getElementById('addUserForm').reset();
                } else {
                    alert(result.message);
                }
            } catch (error) {
                console.error('添加用户失败:', error);
                alert('添加用户失败，请检查网络连接');
            }
        }
        
        // 删除用户
        async function deleteUser(userId) {
            if (confirm('确定要删除这个用户吗？')) {
                try {
                    const result = await apiClient.deleteUser(userId);
                    if (result.success) {
                        alert('用户删除成功');
                        loadUsers();
                        loadSystemStats();
                    } else {
                        alert(result.message);
                    }
                } catch (error) {
                    console.error('删除用户失败:', error);
                    alert('删除用户失败，请检查网络连接');
                }
            }
        }
        
        // 编辑用户（简化版）
        async function editUser(userId) {
            const newRole = prompt('请选择新角色:\n1. user (普通用户)\n2. business_admin (业务管理员)\n3. system_admin (系统管理员)');
            if (newRole && ['user', 'business_admin', 'system_admin'].includes(newRole)) {
                try {
                    const result = await apiClient.updateUserRole(userId, newRole);
                    if (result.success) {
                        alert('用户角色更新成功');
                        loadUsers();
                    } else {
                        alert(result.message);
                    }
                } catch (error) {
                    console.error('更新用户角色失败:', error);
                    alert('更新用户角色失败，请检查网络连接');
                }
            }
        }
        
        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('user');
                window.location.href = 'index.html';
            }
        }
    </script>
</body>
</html>
