<!DOCTYPE html>
<!-- 网页声明为HTML5 -->
<html lang="zh-CN">
<!-- 指定语言为中文 -->
<head>
    <!-- 头部元素 -->
    <meta charset="UTF-8">
    <!-- 字符编码为UTF-8 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 视口设置，确保在不同设备上正常显示 -->
    <title>仓库管理系统</title>
    <!-- 页面标题 -->
    <script src="../scripts/api-client.js"></script>
    <link rel="stylesheet" href="../css/styles.css">
    <style>
        /* 页面特定的CSS样式 */
        /* 系统时间显示样式 */
        .system-time {
            text-align: center;
            margin-bottom: 15px;
            font-size: 16px;
            color: #4caf50;
            font-weight: bold;
            padding: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            animation: pulse 2s infinite;
            transition: all 0.3s ease;
        }
        
        .system-time:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.02);
        }
        body {
            /* 使用open.png作为背景图片 */
            margin: 0; /* 外边距 */
            padding: 0; /* 内边距 */
            display: flex; /* 弹性布局 */
            justify-content: center; /* 水平居中 */
            align-items: center; /* 垂直居中 */
            height: 100vh; /* 视口高度 */
            font-family: 'Microsoft YaHei', Arial, sans-serif; /* 字体 */
            position: relative; /* 相对定位，用于添加覆盖层 */
            animation: fadeIn 1.2s ease-in-out; /* 添加淡入动画效果 */
            background: url('../public/assets/images/finish/open.png') no-repeat center center; /* 使用open背景图片 */
            background-size: cover; /* 背景图片覆盖整个区域 */
        }
        
        /* 系统标题样式 */
        .system-title {
            position: absolute;
            top: 10%;
            left: 50%;
            transform: translateX(-50%);
            font-size: 42px;
            color: #fff;
            text-shadow: 0 0 10px rgba(0,0,0,0.5), 0 0 20px rgba(76, 175, 80, 0.7);
            font-weight: bold;
            letter-spacing: 2px;
            z-index: 10;
            background: rgba(0,0,0,0.3);
            padding: 15px 30px;
            border-radius: 10px;
            backdrop-filter: blur(3px);
            -webkit-backdrop-filter: blur(3px);
            border: 2px solid rgba(255,255,255,0.2);
            animation: titleGlow 2s infinite alternate;
        }
        
        @keyframes titleGlow {
            from { box-shadow: 0 0 10px rgba(76, 175, 80, 0.5); }
            to { box-shadow: 0 0 20px rgba(76, 175, 80, 0.8); }
        }
        
        /* 添加背景覆盖层，增强视觉效果 */
        body:before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background: rgba(0,0,0,0.2); /* 轻微暗化背景 */
            z-index: -1;
            backdrop-filter: blur(1px); /* 轻微模糊效果 */
            -webkit-backdrop-filter: blur(1px); /* Safari支持 */
        }
        
        /* 增强验证码区域样式 */
        .verification-code {
            margin-bottom: 25px;
            background: rgba(255,255,255,0.1);
            border-radius: 6px;
            padding: 5px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .code-image {
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .code-image:hover {
            transform: scale(1.02);
        }
        
        .container {
            /* 容器样式 */
            background-color: rgba(255, 255, 255, 0.65);
            /* 更透明的白色背景 */
            border-radius: 12px;
            /* 边框圆角 */
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2), 0 0 15px rgba(76, 175, 80, 0.3);
            /* 增强阴影效果，添加绿色光晕 */
            padding: 20px;
            /* 减小内边距 */
            width: 300px;
            /* 减小宽度 */
            backdrop-filter: blur(8px);
            /* 增强背景模糊效果 */
            -webkit-backdrop-filter: blur(8px);
            /* Safari支持 */
            border: 1px solid rgba(255, 255, 255, 0.3);
            /* 边框 */
            transition: all 0.3s ease;
            /* 过渡效果 */
            animation: floatIn 0.8s ease-out; /* 添加浮入动画 */
        }
        
        .container:hover {
            /* 鼠标悬停时容器样式 */
            transform: translateY(-5px);
            /* 上移效果 */
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
            /* 增强阴影 */
        }
        
        h2 {
            /* 二级标题样式 */
            text-align: center;
            /* 文本居中 */
            color: #333;
            /* 文本颜色 */
            margin-bottom: 25px;
            /* 下外边距 */
            font-size: 28px;
            /* 字体大小 */
            text-shadow: 1px 1px 3px rgba(0,0,0,0.1);
            /* 文字阴影 */
        }
        
        .tab-container {
            /* 标签容器样式 */
            display: flex;
            /* 弹性布局 */
            margin-bottom: 25px;
            /* 下外边距 */
        }
        
        .tab {
            /* 标签样式 */
            flex: 1;
            /* 弹性比例 */
            text-align: center;
            /* 文本居中 */
            padding: 12px;
            /* 内边距 */
            cursor: pointer;
            /* 鼠标指针 */
            border-bottom: 2px solid #ddd;
            /* 底部边框 */
            transition: all 0.3s;
            /* 过渡效果 */
            font-weight: bold;
            /* 字体粗细 */
            position: relative;
            /* 相对定位 */
            overflow: hidden;
            /* 隐藏溢出 */
        }
        
        .tab:after {
            /* 添加标签底部动画效果 */
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background-color: #4caf50;
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }
        
        .tab.active {
            /* 活动标签样式 */
            border-bottom: 2px solid #4caf50;
            /* 底部边框颜色 */
            color: #4caf50;
            /* 文本颜色 */
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.1);
            /* 添加阴影效果 */
        }
        
        .tab:hover:after {
            /* 悬停时底部动画 */
            width: 100%;
        }
        
        .form-group {
            /* 表单组样式 */
            margin-bottom: 20px;
            /* 下外边距 */
            position: relative;
            /* 相对定位 */
        }
        
        label {
            /* 标签样式 */
            display: block;
            /* 块级显示 */
            margin-bottom: 8px;
            /* 下外边距 */
            font-weight: bold;
            /* 字体粗细 */
            color: #444;
            /* 文本颜色 */
        }
        
        input {
            /* 输入框样式 */
            width: 100%;
            /* 宽度 */
            padding: 12px;
            /* 内边距 */
            box-sizing: border-box;
            /* 盒模型计算方式 */
            border: 1px solid #ddd;
            /* 边框 */
            border-radius: 6px;
            /* 边框圆角 */
            transition: all 0.3s;
            /* 过渡效果 */
            font-size: 16px;
            /* 字体大小 */
        }
        
        input:focus {
            /* 输入框聚焦样式 */
            outline: none;
            /* 移除默认轮廓 */
            border-color: #4caf50;
            /* 边框颜色 */
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
            /* 添加阴影 */
        }
        
        button {
            /* 按钮样式 */
            width: 100%;
            /* 宽度 */
            padding: 12px;
            /* 内边距 */
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            /* 渐变背景 */
            color: white;
            /* 文本颜色 */
            border: none;
            /* 无边框 */
            border-radius: 6px;
            /* 边框圆角 */
            cursor: pointer;
            /* 鼠标指针 */
            font-size: 16px;
            /* 字体大小 */
            font-weight: bold;
            /* 字体粗细 */
            transition: all 0.3s;
            /* 过渡效果 */
            position: relative;
            /* 相对定位，用于添加伪元素 */
            overflow: hidden;
            /* 隐藏溢出内容 */
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
            /* 添加阴影 */
        }
        
        button:before {
            /* 按钮伪元素，添加光效 */
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255, 255, 255, 0.1);
            transform: rotate(45deg);
            transition: all 0.6s;
            opacity: 0;
        }
        
        button:hover {
            /* 鼠标悬停按钮样式 */
            background: linear-gradient(135deg, #45a049 0%, #3d8b3d 100%);
            /* 悬停时的渐变背景 */
            transform: translateY(-2px);
            /* 上移效果 */
            box-shadow: 0 6px 18px rgba(76, 175, 80, 0.4);
            /* 增强阴影效果 */
            animation: glow 1.5s infinite alternate;
            /* 添加发光动画 */
        }
        
        button:hover:before {
            /* 悬停时伪元素动画 */
            opacity: 1;
            left: -10%;
            top: -10%;
        }
        
        .verification-code {
            /* 验证码容器样式 */
            display: flex;
            /* 弹性布局 */
            align-items: center;
            /* 垂直居中 */
            background: rgba(255,255,255,0.1);
            /* 半透明背景 */
            border-radius: 6px;
            /* 圆角 */
            padding: 8px;
            /* 内边距 */
            border: 1px solid rgba(255,255,255,0.2);
            /* 边框 */
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            /* 阴影 */
        }
        
        .code-input {
            /* 验证码输入框样式 */
            flex: 1;
            /* 弹性比例 */
            margin-right: 15px;
            /* 右外边距 */
        }
        
        .code-image {
            /* 验证码图片样式 */
            width: 120px;
            /* 宽度 */
            height: 44px;
            /* 高度 */
            background-color: #f1f1f1;
            /* 背景颜色 */
            display: flex;
            /* 弹性布局 */
            justify-content: center;
            /* 水平居中 */
            align-items: center;
            /* 垂直居中 */
            font-size: 18px;
            /* 字体大小 */
            cursor: pointer;
            /* 鼠标指针 */
            border-radius: 6px;
            /* 边框圆角 */
            font-weight: bold;
            /* 字体粗细 */
            letter-spacing: 2px;
            /* 字母间距 */
            user-select: none;
            /* 禁止选择 */
        }

        /* 新增：加载指示器样式 */
        .loader {
            /* 加载指示器样式 */
            display: none;
            /* 默认隐藏 */
            border: 4px solid rgba(243, 243, 243, 0.7);
            /* 边框样式 */
            border-top: 4px solid #4caf50;
            /* 顶部边框颜色 */
            border-radius: 50%;
            /* 圆形 */
            width: 24px;
            /* 宽度 */
            height: 24px;
            /* 高度 */
            animation: spin 1.5s linear infinite;
            /* 旋转动画 */
            margin: 15px auto;
            /* 外边距 */
        }
        
        @keyframes spin {
            /* 旋转动画定义 */
            0% { transform: rotate(0deg); }
            /* 初始角度 */
            100% { transform: rotate(360deg); }
            /* 结束角度 */
        }

        /* 新增：消息提示样式 */
        .message {
            /* 消息提示样式 */
            padding: 12px;
            /* 内边距 */
            margin-bottom: 20px;
            /* 下外边距 */
            border-radius: 6px;
            /* 边框圆角 */
            text-align: center;
            /* 文本居中 */
            display: none;
            /* 默认隐藏 */
            animation: fadeIn 0.3s;
            /* 淡入动画 */
        }
        
        @keyframes fadeIn {
            /* 淡入动画定义 */
            from { opacity: 0; transform: translateY(-10px); }
            /* 初始状态 */
            to { opacity: 1; transform: translateY(0); }
            /* 最终状态 */
        }
        
        @keyframes floatIn {
            /* 浮入动画定义 */
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes pulse {
            /* 脉冲动画定义 */
            0% { text-shadow: 0 2px 10px rgba(0,0,0,0.3), 0 0 20px rgba(76, 175, 80, 0.5); }
            100% { text-shadow: 0 2px 15px rgba(0,0,0,0.4), 0 0 30px rgba(76, 175, 80, 0.8); }
        }
        
        @keyframes glow {
            /* 发光动画定义 */
            0% { box-shadow: 0 0 5px rgba(76, 175, 80, 0.5); }
            100% { box-shadow: 0 0 20px rgba(76, 175, 80, 0.8); }
        }
        
        .message.success {
            /* 成功消息样式 */
            background-color: #e8f5e9;
            /* 背景颜色 */
            color: #2e7d32;
            /* 文本颜色 */
            border: 1px solid #a5d6a7;
            /* 边框 */
        }
        
        .message.error {
            /* 错误消息样式 */
            background-color: #ffebee;
            /* 背景颜色 */
            color: #c62828;
            /* 文本颜色 */
            border: 1px solid #ef9a9a;
            /* 边框 */
        }
        
        .system-title {
            /* 系统标题样式 */
            font-size: 42px;
            /* 字体大小 */
            color: white;
            /* 文本颜色 */
            text-align: center;
            /* 文本居中 */
            margin-bottom: 30px;
            /* 下外边距 */
            text-shadow: 0 2px 10px rgba(0,0,0,0.5), 0 0 20px rgba(76, 175, 80, 0.8);
            /* 文字阴影增强，添加绿色光晕 */
            font-weight: bold;
            /* 字体粗细 */
            letter-spacing: 2px; /* 字母间距 */
            animation: pulse 2s infinite alternate; /* 添加脉冲动画 */
            position: relative; /* 相对定位 */
            z-index: 10; /* 确保标题在最上层 */
        }

        .user-info {
            /* 用户信息样式 */
            display: flex;
            /* 弹性布局 */
            align-items: center;
            /* 垂直居中 */
            position: relative;
            /* 相对定位 */
        }

        .user-info:before {
            /* 用户图标 */
            content: '';
            /* 内容为空 */
            width: 24px;
            /* 宽度 */
            height: 24px;
            /* 高度 */
            background: url('../public/assets/images/finish/user.jpg') no-repeat center center;
            /* 使用user.jpg作为图标 */
            background-size: contain;
            /* 背景图片适应 */
            margin-right: 10px;
            /* 右外边距 */
            display: inline-block;
            /* 内联块级显示 */
        }

        /* 添加角色选择样式 - 优化紧凑版 */
        .role-selection {
            /* 角色选择容器样式 */
            display: flex;
            /* 弹性布局 */
            flex-direction: row;
            /* 水平排列 */
            justify-content: center;
            /* 水平居中 */
            margin-bottom: 10px;
            /* 减少下外边距 */
            padding: 5px 8px;
            /* 减少内边距 */
            background-color: rgba(245, 245, 245, 0.4);
            /* 更透明的背景 */
            border-radius: 4px;
            /* 减小边框圆角 */
            border: 1px solid rgba(255, 255, 255, 0.2);
            /* 添加细边框 */
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            /* 轻微阴影 */
        }

        .role-option {
            /* 角色选项样式 */
            display: flex;
            /* 弹性布局 */
            align-items: center;
            /* 垂直居中 */
            margin: 0 5px;
            /* 减少外边距 */
            cursor: pointer;
            /* 鼠标指针 */
            transition: transform 0.2s;
            /* 添加过渡效果 */
        }

        .role-option:hover {
            /* 悬停效果 */
            transform: translateY(-1px);
            /* 轻微上浮 */
        }

        .role-option input[type="radio"] {
            /* 单选按钮样式 */
            margin-right: 3px;
            /* 减少右外边距 */
            cursor: pointer;
            /* 鼠标指针 */
            accent-color: #4caf50;
            /* 选中颜色 */
        }

        .role-option label {
            /* 角色标签样式 */
            font-weight: normal;
            /* 字体粗细 */
            cursor: pointer;
            /* 鼠标指针 */
            display: inline;
            /* 内联显示 */
            margin-bottom: 0;
            /* 无下外边距 */
            font-size: 14px;
            /* 稍微减小字体 */
        }

        .role-option:hover label {
            /* 角色标签悬停样式 */
            color: #4caf50;
            /* 文本颜色 */
        }

        /* 输入框图标样式 */
        .input-icon {
            width: 20px; /* 图标宽度 */
            height: 20px; /* 图标高度 */
            vertical-align: middle; /* 垂直居中 */
            margin-right: 8px; /* 图标与文本间距 */
            border-radius: 50%; /* 圆形图标 */
            box-shadow: 0 0 5px rgba(76, 175, 80, 0.5); /* 添加光晕效果 */
            transition: all 0.3s ease; /* 过渡效果 */
        }
        
        .input-icon:hover {
            transform: scale(1.1); /* 悬停放大效果 */
            box-shadow: 0 0 8px rgba(76, 175, 80, 0.8); /* 增强光晕 */
        }
    </style>
</head>
<body>
    <!-- 页面主体 -->
    <div>
        <!-- 主内容区 -->
        <h1 class="system-title">智能仓库管理系统</h1>
        <div class="container">
            <!-- 主容器 -->
            <h2>用户登录</h2>
            <!-- 标题 -->
            <div id="system-time" class="system-time"></div>
            <!-- 系统时间显示 -->
            
            <!-- 新增：消息提示区域 -->
            <div id="messageContainer" class="message"></div>
            
            <!-- 登录/注册切换标签 -->
            <div class="tab-container">
                <!-- 标签容器 -->
                <div class="tab active" onclick="switchTab('login')">登录</div>
                <!-- 登录标签 -->
                <div class="tab" onclick="switchTab('register')">注册</div>
                <!-- 注册标签 -->
            </div>
            
            <!-- 登录表单 -->
            <div id="loginForm">
                <!-- 登录表单 -->
                <div class="form-group">
                    <!-- 用户名输入框，带图标 -->
                    <label for="username"><img src="../public/assets/images/finish/user.jpg" alt="用户图标" class="input-icon">用户名</label>
                    <!-- 用户名标签 -->
                    <input type="text" id="username" placeholder="请输入用户名">
                    <!-- 用户名输入框 -->
                </div>
                <div class="form-group">
                    <!-- 密码输入框，带图标 -->
                    <label for="password"><img src="../public/assets/images/finish/password.png" alt="密码图标" class="input-icon">密码</label>
                    <!-- 密码标签 -->
                    <input type="password" id="password" placeholder="请输入密码">
                    <!-- 密码输入框 -->
                </div>
                <div class="form-group">
                    <!-- 表单组 -->
                    <label for="role">角色</label>
                    <!-- 角色标签 -->
                    <div class="role-selection">
                        <div class="role-option">
                            <input type="radio" id="roleSysAdmin" name="role" value="system_admin">
                            <label for="roleSysAdmin">系统管理员</label>
                        </div>
                        <div class="role-option">
                            <input type="radio" id="roleBizAdmin" name="role" value="business_admin">
                            <label for="roleBizAdmin">业务管理员</label>
                        </div>
                        <div class="role-option">
                            <input type="radio" id="roleUser" name="role" value="user" checked>
                            <label for="roleUser">普通用户</label>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <!-- 表单组 -->
                    <label for="loginVerification"><img src="../public/assets/images/finish/update.png" alt="验证码图标" class="input-icon">验证码</label>
                    <!-- 验证码标签 -->
                    <div class="verification-code">
                        <!-- 验证码容器 -->
                        <input type="text" id="loginVerification" class="code-input" placeholder="请输入验证码">
                        <!-- 验证码输入框 -->
                        <div class="code-image" id="loginVerificationCode" onclick="refreshVerificationCode('loginVerificationCode')">
                            <!-- 验证码图片 -->
                            8888
                            <!-- 默认验证码 -->
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <!-- 表单组 -->
                    <button onclick="login()" id="loginButton"><img src="../public/assets/images/finish/login.png" alt="登录图标" class="input-icon" style="margin-right: 5px;">登录</button>
                    <!-- 登录按钮 -->
                </div>
            </div>
            <div id="loginError" style="color: #c62828; text-align: center; margin-top: 10px;"></div>
            
            <!-- 注册表单 -->
            <div id="registerForm" style="display: none;">
                <!-- 注册表单，默认隐藏 -->
                <div class="form-group">
                    <!-- 注册用户名输入框，带图标 -->
                    <label for="registerUsername"><img src="../public/assets/images/finish/user.jpg" alt="用户图标" class="input-icon">用户名</label>
                    <!-- 用户名标签 -->
                    <input type="text" id="registerUsername" placeholder="请输入用户名">
                    <!-- 用户名输入框 -->
                </div>
                <div class="form-group">
                    <!-- 注册密码输入框，带图标 -->
                    <label for="registerPassword"><img src="../public/assets/images/finish/password.png" alt="密码图标" class="input-icon">密码</label>
                    <!-- 密码标签 -->
                    <input type="password" id="registerPassword" placeholder="请输入密码">
                    <!-- 密码输入框 -->
                </div>
                <div class="form-group">
                    <!-- 确认密码输入框，带图标 -->
                    <label for="registerConfirmPassword"><img src="../public/assets/images/finish/password.png" alt="密码图标" class="input-icon">确认密码</label>
                    <!-- 确认密码标签 -->
                    <input type="password" id="registerConfirmPassword" placeholder="请再次输入密码">
                    <!-- 确认密码输入框 -->
                </div>
                <div class="form-group">
                    <!-- 表单组 -->
                    <label for="registerRole">角色</label>
                    <!-- 角色标签 -->
                    <div class="role-selection">
                        <div class="role-option">
                            <input type="radio" id="registerRoleSysAdmin" name="registerRole" value="system_admin">
                            <label for="registerRoleSysAdmin">系统管理员</label>
                        </div>
                        <div class="role-option">
                            <input type="radio" id="registerRoleBizAdmin" name="registerRole" value="business_admin">
                            <label for="registerRoleBizAdmin">业务管理员</label>
                        </div>
                        <div class="role-option">
                            <input type="radio" id="registerRoleUser" name="registerRole" value="user" checked>
                            <label for="registerRoleUser">普通用户</label>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <!-- 表单组 -->
                    <label for="registerVerification"><img src="../public/assets/images/finish/update.png" alt="验证码图标" class="input-icon">验证码</label>
                    <!-- 验证码标签 -->
                    <div class="verification-code">
                        <!-- 验证码容器 -->
                        <input type="text" id="registerVerification" class="code-input" placeholder="请输入验证码">
                        <!-- 验证码输入框 -->
                        <div class="code-image" id="registerVerificationCode" onclick="refreshVerificationCode('registerVerificationCode')">
                            <!-- 验证码图片 -->
                            8888
                            <!-- 默认验证码 -->
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <!-- 表单组 -->
                    <button onclick="register()"><img src="../public/assets/images/finish/enroll.png" alt="注册图标" class="input-icon" style="margin-right: 5px;">注册</button>
                    <!-- 注册按钮 -->
                </div>
            </div>
            
            <!-- 加载中指示器 -->
            <div id="loader" class="loader"></div>
        </div>
    </div>

    <script>
        // JavaScript代码部分
        let currentTab = 'login'; // 当前标签，默认为登录
        let verificationCode = generateCode(); // 生成验证码
        let regVerificationCode = generateCode(); // 注册表单验证码
        
        // 更新系统时间显示
        function updateSystemTime() {
            const now = new Date();
            const options = { 
                year: 'numeric', 
                month: '2-digit', 
                day: '2-digit',
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit',
                hour12: false
            };
            const timeString = now.toLocaleString('zh-CN', options);
            document.getElementById('system-time').textContent = '系统时间：' + timeString;
        }
        
        // 页面加载时设置验证码
        document.addEventListener('DOMContentLoaded', function() {
            // DOM内容加载完成后执行
            document.getElementById('loginVerificationCode').innerText = verificationCode;
            // 设置登录验证码图片内容
            document.getElementById('registerVerificationCode').innerText = regVerificationCode;
            // 设置注册验证码图片内容
            
            // 初始化系统时间并设置定时更新
            updateSystemTime();
            // 每秒更新一次时间
            setInterval(updateSystemTime, 1000);
        });
        
        // 切换标签函数
        function switchTab(tab) {
            // 切换标签
            currentTab = tab; // 更新当前标签
            
            // 移除所有标签的活动状态
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(t => t.classList.remove('active'));
            
            // 激活当前标签（找到包含相应文本的标签）
            const activeTab = Array.from(tabs).find(t => t.textContent.trim().toLowerCase().includes(tab));
            if (activeTab) {
                activeTab.classList.add('active');
            }
            
            // 显示/隐藏相应表单
            document.getElementById('loginForm').style.display = tab === 'login' ? 'block' : 'none';
            document.getElementById('registerForm').style.display = tab === 'register' ? 'block' : 'none';
            
            // 隐藏错误消息
            document.getElementById('loginError').textContent = '';
            hideMessage();
        }
        
        // 生成随机验证码
        function generateCode() {
            // 生成验证码
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'; // 字符集
            let code = ''; // 初始化验证码
            for (let i = 0; i < 4; i++) {
                // 循环生成4个字符
                code += chars.charAt(Math.floor(Math.random() * chars.length)); // 随机选择字符
            }
            return code; // 返回生成的验证码
        }
        
        // 刷新验证码
        function refreshVerificationCode(codeId) {
            // 刷新验证码
            verificationCode = generateCode(); // 重新生成验证码
            document.getElementById(codeId).innerText = verificationCode; // 更新验证码显示
        }
        
        // 刷新注册验证码
        function refreshRegVerificationCode() {
            // 刷新注册验证码
            regVerificationCode = generateCode(); // 重新生成验证码
            document.getElementById('registerVerificationCode').innerText = regVerificationCode; // 更新验证码显示
        }
        
        // 显示注册表单
        function showRegisterForm() {
            // 显示注册表单
            document.getElementById('loginForm').style.display = 'none'; // 隐藏登录表单
            document.getElementById('registerForm').style.display = 'block'; // 显示注册表单
            hideMessage(); // 隐藏消息
        }
        
        // 返回登录表单
        function backToLogin() {
            // 返回登录表单
            document.getElementById('registerForm').style.display = 'none'; // 隐藏注册表单
            document.getElementById('loginForm').style.display = 'block'; // 显示登录表单
            hideMessage(); // 隐藏消息
        }
        
        // 显示消息
        function showMessage(message, type) {
            // 显示消息
            const messageContainer = document.getElementById('messageContainer'); // 获取消息容器
            messageContainer.innerText = message; // 设置消息内容
            messageContainer.classList.remove('success', 'error'); // 移除所有消息类型样式
            messageContainer.classList.add(type); // 添加当前消息类型样式
            messageContainer.style.display = 'block'; // 显示消息容器
        }
        
        // 隐藏消息
        function hideMessage() {
            // 隐藏消息
            document.getElementById('messageContainer').style.display = 'none'; // 隐藏消息容器
        }
        
        // 显示加载指示器
        function showLoader() {
            // 显示加载指示器
            document.getElementById('loader').style.display = 'block'; // 显示加载指示器
        }
        
        // 隐藏加载指示器
        function hideLoader() {
            // 隐藏加载指示器
            document.getElementById('loader').style.display = 'none'; // 隐藏加载指示器
        }
        
        // 登录函数
        function login() {
            // 获取用户名、密码、角色和验证码
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            // 获取选中的角色
            let role = 'user'; // 默认角色
            if (document.getElementById('roleSysAdmin').checked) {
                role = 'system_admin';
            } else if (document.getElementById('roleBizAdmin').checked) {
                role = 'business_admin';
            } else if (document.getElementById('roleUser').checked) {
                role = 'user';
            }

            const code = document.getElementById('loginVerification').value;

            // 验证用户名和密码不为空
            if (!username || !password) {
                document.getElementById('loginError').textContent = '请输入用户名和密码';
                return;
            }

            // 验证验证码是否正确
            if (!code || code.toLowerCase() !== verificationCode.toLowerCase()) {
                document.getElementById('loginError').textContent = '验证码不正确';
                refreshVerificationCode('loginVerificationCode');
                return;
            }

            // 显示加载提示
            document.getElementById('loginButton').disabled = true;
            document.getElementById('loginButton').innerHTML = '<img src="../public/assets/images/finish/login.png" alt="登录图标" class="input-icon" style="margin-right: 5px;">登录中...';
            document.getElementById('loginError').textContent = '';

            // 使用API客户端进行登录
            apiClient.login(username, password, role).then(result => {
                if (result.success) {
                    // 登录成功，保存用户信息
                    localStorage.setItem('user', JSON.stringify({
                        username: result.user.username,
                        role: result.user.role,
                        id: result.user.id,
                        real_name: result.user.real_name
                    }));

                    // 显示成功消息
                    showMessage('登录成功，正在跳转...', 'success');

                    // 根据角色重定向
                    setTimeout(() => {
                        if (result.user.role === 'system_admin') {
                            window.location.href = 'system_admin_dashboard.html';
                        } else if (result.user.role === 'business_admin') {
                            window.location.href = 'business_admin_dashboard.html';
                        } else {
                            window.location.href = 'user_dashboard.html';
                        }
                    }, 1000);
                } else {
                    // 登录失败
                    document.getElementById('loginError').textContent = result.message;
                    document.getElementById('loginButton').disabled = false;
                    document.getElementById('loginButton').innerHTML = '<img src="../public/assets/images/finish/login.png" alt="登录图标" class="input-icon" style="margin-right: 5px;">登录';
                }
            }).catch(error => {
                console.error('登录错误:', error);
                document.getElementById('loginError').textContent = '登录失败，请检查网络连接';
                document.getElementById('loginButton').disabled = false;
                document.getElementById('loginButton').innerHTML = '<img src="../public/assets/images/finish/login.png" alt="登录图标" class="input-icon" style="margin-right: 5px;">登录';
            });
        }
        
        // 注册用户
        function register() {
            // 注册函数
            const username = document.getElementById('registerUsername').value; // 获取注册用户名
            const password = document.getElementById('registerPassword').value; // 获取注册密码
            const confirmPass = document.getElementById('registerConfirmPassword').value; // 获取确认密码
            const code = document.getElementById('registerVerification').value; // 获取验证码
            
            // 获取选中的角色
            let role = 'user'; // 默认角色
            if (document.getElementById('registerRoleSysAdmin').checked) {
                role = 'system_admin';
            } else if (document.getElementById('registerRoleBizAdmin').checked) {
                role = 'business_admin';
            } else if (document.getElementById('registerRoleUser').checked) {
                role = 'user';
            }
            
            if (!username || !password || !confirmPass) {
                // 检查输入是否为空
                showMessage('请填写所有字段！', 'error'); // 显示错误消息
                return; // 返回
            }
            
            if (password !== confirmPass) {
                // 检查两次密码是否一致
                showMessage('两次输入的密码不一致！', 'error'); // 显示错误消息
                return; // 返回
            }
            
            if (code.toLowerCase() !== regVerificationCode.toLowerCase()) {
                // 检查验证码是否正确
                showMessage('验证码不正确！', 'error'); // 显示错误消息
                refreshRegVerificationCode(); // 刷新验证码
                return; // 返回
            }
            
            // 显示加载指示器
            showLoader(); // 显示加载指示器
            
            // 使用API客户端进行注册
            apiClient.register(username, password, role).then(result => {
                hideLoader(); // 隐藏加载指示器

                if (result.success) {
                    // 注册成功
                    showMessage(result.message, 'success'); // 显示成功消息

                    // 延时返回登录页面
                    setTimeout(() => {
                        backToLogin(); // 返回登录页面
                    }, 1500); // 延时1.5秒
                } else {
                    // 注册失败
                    showMessage(result.message, 'error'); // 显示错误消息
                }
            }).catch(error => {
                hideLoader();
                console.error('注册错误:', error);
                showMessage('注册失败，请检查网络连接', 'error');
            });
        }

        // 更新系统时间
        function updateSystemTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                weekday: 'long'
            });
            const timeElement = document.getElementById('systemTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // 页面加载时启动时间更新
        document.addEventListener('DOMContentLoaded', function() {
            updateSystemTime();
            setInterval(updateSystemTime, 1000);
        });
    </script>

    <!-- 系统时间和开发者信息 -->
    <div class="footer-info">
        <div class="system-time">
            <i class="fas fa-clock me-2"></i>
            <span id="systemTime"></span>
        </div>
        <div class="developer-info">
            <p class="mb-1">
                <i class="fas fa-code me-2"></i>
                开发者：温宇博 | 学号：22219010512 | 班级：22机计算机科学与技术5班
            </p>
            <p class="mb-0">
                <i class="fas fa-envelope me-2"></i>
                如有问题请联系开发者 | 欢迎使用智能仓库管理系统
            </p>
        </div>
    </div>

    <style>
        .footer-info {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px 20px;
            font-size: 0.85rem;
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255,255,255,0.1);
            z-index: 1000;
        }

        .system-time {
            text-align: center;
            margin-bottom: 8px;
            font-weight: 600;
            color: #4CAF50;
        }

        .developer-info {
            text-align: center;
            line-height: 1.4;
        }

        .developer-info p {
            margin: 2px 0;
        }

        @media (max-width: 768px) {
            .footer-info {
                font-size: 0.75rem;
                padding: 10px 15px;
            }
        }
    </style>
</body>
</html>