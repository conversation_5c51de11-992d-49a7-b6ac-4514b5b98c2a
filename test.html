<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            margin: 5px;
            padding: 8px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>仓库管理系统 API 测试</h1>
    
    <div class="test-section">
        <h3>1. 测试登录</h3>
        <button onclick="testLogin('sysadmin', 'sys123', 'system_admin')">系统管理员登录</button>
        <button onclick="testLogin('bizadmin', 'biz123', 'business_admin')">业务管理员登录</button>
        <button onclick="testLogin('user', 'password', 'user')">普通用户登录</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 测试获取用户列表</h3>
        <button onclick="testGetUsers()">获取所有用户</button>
        <div id="usersResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 测试获取库存</h3>
        <button onclick="testGetInventory()">获取库存列表</button>
        <div id="inventoryResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 测试获取申请</h3>
        <button onclick="testGetRequests()">获取申请列表</button>
        <div id="requestsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>5. 测试系统统计</h3>
        <button onclick="testGetStats()">获取系统统计</button>
        <div id="statsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>6. 测试添加物品</h3>
        <button onclick="testAddItem()">添加测试物品</button>
        <div id="addItemResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>7. 测试提交申请</h3>
        <button onclick="testSubmitRequest()">提交测试申请</button>
        <div id="submitRequestResult" class="result"></div>
    </div>

    <script>
        // 基础API调用函数
        async function apiCall(endpoint, options = {}) {
            const url = `http://localhost:3000/api${endpoint}`;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    'x-user': localStorage.getItem('currentTestUser') || 'test'
                },
                ...options
            };

            try {
                const response = await fetch(url, config);
                const data = await response.json();
                return data;
            } catch (error) {
                return {
                    success: false,
                    message: '网络请求失败: ' + error.message
                };
            }
        }

        // 测试登录
        async function testLogin(username, password, role) {
            const result = await apiCall('/login', {
                method: 'POST',
                body: JSON.stringify({ username, password, role })
            });
            
            document.getElementById('loginResult').textContent = JSON.stringify(result, null, 2);
            
            if (result.success) {
                localStorage.setItem('currentTestUser', username);
                alert(`${role} 登录成功！`);
            }
        }

        // 测试获取用户列表
        async function testGetUsers() {
            const result = await apiCall('/users');
            document.getElementById('usersResult').textContent = JSON.stringify(result, null, 2);
        }

        // 测试获取库存
        async function testGetInventory() {
            const result = await apiCall('/inventory');
            document.getElementById('inventoryResult').textContent = JSON.stringify(result, null, 2);
        }

        // 测试获取申请
        async function testGetRequests() {
            const result = await apiCall('/requests');
            document.getElementById('requestsResult').textContent = JSON.stringify(result, null, 2);
        }

        // 测试系统统计
        async function testGetStats() {
            const result = await apiCall('/stats');
            document.getElementById('statsResult').textContent = JSON.stringify(result, null, 2);
        }

        // 测试添加物品
        async function testAddItem() {
            const itemData = {
                name: '测试物品_' + Date.now(),
                category: '测试分类',
                price: 99.99,
                supplier: '测试供应商',
                description: '这是一个测试物品',
                min_stock: 5,
                max_stock: 50
            };

            const result = await apiCall('/inventory', {
                method: 'POST',
                body: JSON.stringify(itemData)
            });
            
            document.getElementById('addItemResult').textContent = JSON.stringify(result, null, 2);
        }

        // 测试提交申请
        async function testSubmitRequest() {
            const requestData = {
                item: '笔记本电脑',
                quantity: 2,
                purpose: '测试申请',
                type: 'outbound'
            };

            const result = await apiCall('/requests', {
                method: 'POST',
                body: JSON.stringify(requestData)
            });
            
            document.getElementById('submitRequestResult').textContent = JSON.stringify(result, null, 2);
        }

        // 页面加载时的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('API测试页面已加载');
            console.log('请先测试登录功能，然后测试其他API');
        });
    </script>
</body>
</html>
